.PHONY: help run setup clean test docker-up docker-down

# Default target
help:
	@echo "Available commands:"
	@echo "  setup      - Install dependencies"
	@echo "  docker-up  - Start Weaviate with Docker Compose"
	@echo "  docker-down- Stop Weaviate"
	@echo "  run        - Run the example application"
	@echo "  test       - Run tests"
	@echo "  clean      - Clean up generated files"

# Install dependencies
setup:
	go mod tidy
	go mod download

# Start Weaviate using Docker Compose
docker-up:
	docker-compose up -d
	@echo "Waiting for Weaviate to be ready..."
	@sleep 10
	@curl -f http://localhost:8080/v1/meta > /dev/null 2>&1 && echo "✅ Weaviate is ready!" || echo "❌ Weaviate not ready yet, please wait a bit more"

# Stop Weaviate
docker-down:
	docker-compose down

# Run the example application
run:
	go run .

# Run tests (if any)
test:
	go test ./...

# Clean up
clean:
	go clean
	docker-compose down -v
