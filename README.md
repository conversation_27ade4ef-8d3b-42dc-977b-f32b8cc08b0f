# Weaviate Go Example Application

This is a comprehensive example application demonstrating how to use Weaviate vector database with Go. The application showcases various Weaviate features including schema creation, data insertion, and different types of searches.

## Features Demonstrated

- 🔗 **Connection Setup**: Connect to local Weaviate instance or Weaviate Cloud Service
- 📚 **Schema Management**: Create collections with proper vectorization settings
- 📝 **Data Operations**: Batch insert articles and products
- 🔍 **Search Capabilities**:
  - Vector/Semantic Search
  - Keyword/BM25 Search
  - Hybrid Search (combining vector and keyword)
  - Filtered Search with conditions
  - Aggregation queries
  - Complex queries with multiple conditions

## Prerequisites

- Go 1.21 or later
- Docker and Docker Compose (for local Weaviate instance)

## Quick Start

### 1. Start Weaviate

Start a local Weaviate instance using Docker Compose:

```bash
docker-compose up -d
```

Wait for Weaviate to be ready (usually takes 1-2 minutes):

```bash
# Check if Weaviate is ready
curl http://localhost:8080/v1/meta
```

### 2. Install Dependencies

```bash
go mod tidy
```

### 3. Run the Application

```bash
go run .
```

## Project Structure

```
.
├── main.go           # Main application entry point
├── models.go         # Data models (Article, Product)
├── collection.go     # Schema creation and collection management
├── data.go          # Sample data insertion
├── search.go        # Search operations and examples
├── docker-compose.yml # Local Weaviate setup
├── go.mod           # Go module dependencies
└── README.md        # This file
```

## Code Overview

### Main Components

1. **Connection Setup** (`main.go`):
   - Initializes Weaviate client
   - Handles health checks
   - Orchestrates the demo flow

2. **Schema Management** (`collection.go`):
   - Creates Article and Product collections
   - Defines properties and vectorization settings
   - Uses text2vec-transformers for local vectorization

3. **Data Models** (`models.go`):
   - Defines Article and Product structures
   - Includes search result types

4. **Data Operations** (`data.go`):
   - Inserts sample articles and products
   - Uses batch operations for efficiency
   - Includes realistic sample data

5. **Search Operations** (`search.go`):
   - Vector search using semantic similarity
   - Keyword search using BM25
   - Hybrid search combining both approaches
   - Filtered searches with conditions
   - Aggregation queries

### Sample Data

The application includes:
- **5 Articles**: Technology, environment, and security topics
- **5 Products**: Electronics, fitness, food, furniture, and eco-friendly items

## Search Examples

### Vector Search
Finds semantically similar content:
```go
// Searches for articles similar to "technology innovation artificial intelligence"
performVectorSearch(client, "technology innovation artificial intelligence")
```

### Keyword Search
Traditional keyword-based search:
```go
// Searches for articles containing "golang programming"
performKeywordSearch(client, "golang programming")
```

### Hybrid Search
Combines vector and keyword search:
```go
// Uses both semantic and keyword matching
performHybridSearch(client, "machine learning tutorial")
```

### Filtered Search
Search with conditions:
```go
// Finds articles in "Technology" category
performFilteredSearch(client, "Technology")
```

## Configuration Options

### Local Development
The default configuration connects to a local Weaviate instance:
```go
cfg := weaviate.Config{
    Host:   "localhost:8080",
    Scheme: "http",
}
```

### Weaviate Cloud Service (WCS)
To use Weaviate Cloud Service, uncomment and modify:
```go
cfg := weaviate.Config{
    Host:   "your-cluster-url.weaviate.network",
    Scheme: "https",
    AuthConfig: auth.ApiKey{Value: "your-api-key"},
    Headers: map[string]string{
        "X-OpenAI-Api-Key": "your-openai-api-key", // if using OpenAI
    },
}
```

## Extending the Example

### Adding New Collections
1. Define a new struct in `models.go`
2. Create a collection function in `collection.go`
3. Add sample data in `data.go`
4. Implement search functions in `search.go`

### Using Different Vectorizers
Modify the vectorizer in collection creation:
```go
Vectorizer: "text2vec-openai", // or other vectorizers
```

### Adding More Search Types
Weaviate supports additional search capabilities:
- Generative search (RAG)
- Multi-modal search
- Cross-references
- Conditional filters

## Troubleshooting

### Common Issues

1. **Connection Failed**: Ensure Weaviate is running and accessible
2. **Schema Errors**: Check if collections already exist
3. **Import Errors**: Verify data format and required fields
4. **Search Issues**: Ensure data is indexed (wait after insertion)

### Debugging

Enable verbose logging:
```bash
export WEAVIATE_LOG_LEVEL=debug
go run .
```

## Resources

- [Weaviate Documentation](https://weaviate.io/developers/weaviate)
- [Go Client Documentation](https://weaviate.io/developers/weaviate/client-libraries/go)
- [Weaviate GitHub](https://github.com/weaviate/weaviate)
- [Go Client GitHub](https://github.com/weaviate/weaviate-go-client)

## License

This example is provided as-is for educational purposes.
