package main

import (
	"context"
	"fmt"

	"github.com/weaviate/weaviate-go-client/v5/weaviate"
)

// createCollections creates the necessary collections in Weaviate
func createCollections(client *weaviate.Client) error {
	ctx := context.Background()

	// Create Article collection
	if err := createArticleCollection(ctx, client); err != nil {
		return fmt.Errorf("failed to create Article collection: %w", err)
	}

	// Create Product collection
	if err := createProductCollection(ctx, client); err != nil {
		return fmt.Errorf("failed to create Product collection: %w", err)
	}

	fmt.Println("✅ Collections created successfully")
	return nil
}

// createArticleCollection creates the Article collection schema
func createArticleCollection(ctx context.Context, client *weaviate.Client) error {
	// Check if collection already exists
	exists, err := client.Schema().ClassExistenceChecker().WithClassName("Article").Do(ctx)
	if err != nil {
		return fmt.E<PERSON><PERSON>("failed to check if Article class exists: %w", err)
	}

	if exists {
		fmt.Println("📚 Article collection already exists")
		return nil
	}

	// Define the Article class schema using map[string]interface{}
	classObj := map[string]interface{}{
		"class":       "Article",
		"description": "A news article or blog post",
		"vectorizer":  "text2vec-transformers",
		"properties": []map[string]interface{}{
			{
				"name":         "title",
				"dataType":     []string{"text"},
				"description":  "The title of the article",
				"tokenization": "word",
			},
			{
				"name":         "content",
				"dataType":     []string{"text"},
				"description":  "The main content of the article",
				"tokenization": "word",
			},
			{
				"name":        "author",
				"dataType":    []string{"text"},
				"description": "The author of the article",
			},
			{
				"name":        "category",
				"dataType":    []string{"text"},
				"description": "The category of the article",
			},
			{
				"name":        "publishedAt",
				"dataType":    []string{"date"},
				"description": "When the article was published",
			},
			{
				"name":        "tags",
				"dataType":    []string{"text[]"},
				"description": "Tags associated with the article",
			},
			{
				"name":        "url",
				"dataType":    []string{"text"},
				"description": "The URL of the article",
			},
		},
	}

	// Create the class
	err = client.Schema().ClassCreator().WithClass(classObj).Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to create Article class: %w", err)
	}

	fmt.Println("📚 Article collection created")
	return nil
}

// createProductCollection creates the Product collection schema
func createProductCollection(ctx context.Context, client *weaviate.Client) error {
	// Check if collection already exists
	exists, err := client.Schema().ClassExistenceChecker().WithClassName("Product").Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to check if Product class exists: %w", err)
	}

	if exists {
		fmt.Println("🛍️ Product collection already exists")
		return nil
	}

	// Define the Product class schema using map[string]interface{}
	classObj := map[string]interface{}{
		"class":       "Product",
		"description": "An e-commerce product",
		"vectorizer":  "text2vec-transformers",
		"properties": []map[string]interface{}{
			{
				"name":         "name",
				"dataType":     []string{"text"},
				"description":  "The name of the product",
				"tokenization": "word",
			},
			{
				"name":         "description",
				"dataType":     []string{"text"},
				"description":  "The description of the product",
				"tokenization": "word",
			},
			{
				"name":        "category",
				"dataType":    []string{"text"},
				"description": "The category of the product",
			},
			{
				"name":        "brand",
				"dataType":    []string{"text"},
				"description": "The brand of the product",
			},
			{
				"name":        "price",
				"dataType":    []string{"number"},
				"description": "The price of the product",
			},
			{
				"name":        "inStock",
				"dataType":    []string{"boolean"},
				"description": "Whether the product is in stock",
			},
			{
				"name":        "tags",
				"dataType":    []string{"text[]"},
				"description": "Tags associated with the product",
			},
		},
	}

	// Create the class
	err = client.Schema().ClassCreator().WithClass(classObj).Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to create Product class: %w", err)
	}

	fmt.Println("🛍️ Product collection created")
	return nil
}
