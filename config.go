package main

import (
	"os"

	"github.com/weaviate/weaviate-go-client/v5/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/auth"
)

// Config holds application configuration
type Config struct {
	WeaviateHost   string
	WeaviateScheme string
	WeaviateAPIKey string
	OpenAIAPIKey   string
	Environment    string
}

// LoadConfig loads configuration from environment variables
func LoadConfig() *Config {
	return &Config{
		WeaviateHost:   getEnv("WEAVIATE_HOST", "localhost:8080"),
		WeaviateScheme: getEnv("WEAVIATE_SCHEME", "http"),
		WeaviateAPIKey: getEnv("WEAVIATE_API_KEY", ""),
		OpenAIAPIKey:   getEnv("OPENAI_API_KEY", ""),
		Environment:    getEnv("ENVIRONMENT", "local"),
	}
}

// CreateWeaviateConfig creates a Weaviate client configuration
func (c *Config) CreateWeaviateConfig() weaviate.Config {
	cfg := weaviate.Config{
		Host:   c.WeaviateHost,
		Scheme: c.WeaviateScheme,
	}

	// Add authentication if API key is provided
	if c.WeaviateAPIKey != "" {
		cfg.AuthConfig = auth.ApiKey{Value: c.WeaviateAPIKey}
	}

	// Add headers if needed
	if c.OpenAIAPIKey != "" {
		cfg.Headers = map[string]string{
			"X-OpenAI-Api-Key": c.OpenAIAPIKey,
		}
	}

	return cfg
}

// getEnv gets an environment variable with a default value
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
