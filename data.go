package main

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/weaviate/weaviate-go-client/v5/weaviate"
)

// insertSampleData inserts sample articles and products into Weaviate
func insertSampleData(client *weaviate.Client) error {
	ctx := context.Background()

	// Insert sample articles
	if err := insertSampleArticles(ctx, client); err != nil {
		return fmt.Errorf("failed to insert articles: %w", err)
	}

	// Insert sample products
	if err := insertSampleProducts(ctx, client); err != nil {
		return fmt.Errorf("failed to insert products: %w", err)
	}

	fmt.Println("✅ Sample data inserted successfully")
	return nil
}

// insertSampleArticles inserts sample articles
func insertSampleArticles(ctx context.Context, client *weaviate.Client) error {
	articles := []Article{
		{
			ID:          uuid.New().String(),
			Title:       "Introduction to Machine Learning with Go",
			Content:     "Machine learning is revolutionizing software development. In this comprehensive guide, we explore how to implement ML algorithms using Go programming language. We'll cover neural networks, data preprocessing, and model training techniques that make Go a powerful choice for ML applications.",
			Author:      "<PERSON>",
			Category:    "Technology",
			PublishedAt: time.Now().AddDate(0, 0, -5),
			Tags:        []string{"golang", "machine-learning", "programming", "tutorial"},
			URL:         "https://example.com/ml-with-go",
		},
		{
			ID:          uuid.New().String(),
			Title:       "Building Scalable Web APIs with Go",
			Content:     "Go's concurrency model and performance characteristics make it ideal for building scalable web APIs. This article covers best practices for API design, middleware implementation, database integration, and deployment strategies for Go-based web services.",
			Author:      "John Doe",
			Category:    "Technology",
			PublishedAt: time.Now().AddDate(0, 0, -10),
			Tags:        []string{"golang", "api", "web-development", "scalability"},
			URL:         "https://example.com/go-web-apis",
		},
		{
			ID:          uuid.New().String(),
			Title:       "The Future of Artificial Intelligence",
			Content:     "Artificial Intelligence continues to evolve at an unprecedented pace. From natural language processing to computer vision, AI technologies are transforming industries. This article explores emerging trends, ethical considerations, and the potential impact of AI on society.",
			Author:      "Dr. Sarah Johnson",
			Category:    "Technology",
			PublishedAt: time.Now().AddDate(0, 0, -3),
			Tags:        []string{"artificial-intelligence", "future-tech", "ethics", "innovation"},
			URL:         "https://example.com/future-of-ai",
		},
		{
			ID:          uuid.New().String(),
			Title:       "Sustainable Technology Practices",
			Content:     "As technology companies grow, so does their environmental impact. This article discusses sustainable development practices, green computing initiatives, and how organizations can reduce their carbon footprint while maintaining technological innovation.",
			Author:      "Mike Green",
			Category:    "Environment",
			PublishedAt: time.Now().AddDate(0, 0, -7),
			Tags:        []string{"sustainability", "green-tech", "environment", "corporate-responsibility"},
			URL:         "https://example.com/sustainable-tech",
		},
		{
			ID:          uuid.New().String(),
			Title:       "Cybersecurity Best Practices for Developers",
			Content:     "Security should be a primary concern for every developer. This comprehensive guide covers secure coding practices, common vulnerabilities, authentication mechanisms, and tools for building secure applications. Learn how to protect your applications from modern threats.",
			Author:      "Alex Security",
			Category:    "Security",
			PublishedAt: time.Now().AddDate(0, 0, -1),
			Tags:        []string{"cybersecurity", "secure-coding", "authentication", "best-practices"},
			URL:         "https://example.com/cybersecurity-guide",
		},
	}

	// Insert articles using batch operation
	batcher := client.Batch().ObjectsBatcher()
	
	for _, article := range articles {
		obj := map[string]interface{}{
			"title":       article.Title,
			"content":     article.Content,
			"author":      article.Author,
			"category":    article.Category,
			"publishedAt": article.PublishedAt.Format(time.RFC3339),
			"tags":        article.Tags,
			"url":         article.URL,
		}

		batcher = batcher.WithObjects(map[string]interface{}{
			"class":      "Article",
			"id":         article.ID,
			"properties": obj,
		})
	}

	// Execute batch insert
	_, err := batcher.Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to insert articles: %w", err)
	}

	fmt.Printf("📚 Inserted %d articles\n", len(articles))
	return nil
}

// insertSampleProducts inserts sample products
func insertSampleProducts(ctx context.Context, client *weaviate.Client) error {
	products := []Product{
		{
			ID:          uuid.New().String(),
			Name:        "Wireless Bluetooth Headphones",
			Description: "Premium quality wireless headphones with active noise cancellation, 30-hour battery life, and superior sound quality. Perfect for music lovers and professionals who demand the best audio experience.",
			Category:    "Electronics",
			Brand:       "AudioTech",
			Price:       299.99,
			InStock:     true,
			Tags:        []string{"headphones", "wireless", "bluetooth", "noise-cancellation"},
		},
		{
			ID:          uuid.New().String(),
			Name:        "Smart Fitness Tracker",
			Description: "Advanced fitness tracker with heart rate monitoring, GPS tracking, sleep analysis, and smartphone integration. Track your workouts, monitor your health, and achieve your fitness goals.",
			Category:    "Fitness",
			Brand:       "FitLife",
			Price:       199.99,
			InStock:     true,
			Tags:        []string{"fitness", "tracker", "health", "smartwatch"},
		},
		{
			ID:          uuid.New().String(),
			Name:        "Organic Coffee Beans",
			Description: "Premium organic coffee beans sourced from sustainable farms. Rich, full-bodied flavor with notes of chocolate and caramel. Perfect for espresso, drip coffee, or French press brewing.",
			Category:    "Food & Beverage",
			Brand:       "BrewMaster",
			Price:       24.99,
			InStock:     true,
			Tags:        []string{"coffee", "organic", "premium", "sustainable"},
		},
		{
			ID:          uuid.New().String(),
			Name:        "Ergonomic Office Chair",
			Description: "Professional ergonomic office chair with lumbar support, adjustable height, and breathable mesh back. Designed for long hours of comfortable work and improved posture.",
			Category:    "Furniture",
			Brand:       "ComfortWork",
			Price:       449.99,
			InStock:     false,
			Tags:        []string{"office", "chair", "ergonomic", "furniture"},
		},
		{
			ID:          uuid.New().String(),
			Name:        "Portable Solar Charger",
			Description: "Eco-friendly portable solar charger with multiple USB ports and fast charging capability. Perfect for outdoor adventures, camping, and emergency situations. Weather-resistant design.",
			Category:    "Electronics",
			Brand:       "EcoCharge",
			Price:       89.99,
			InStock:     true,
			Tags:        []string{"solar", "charger", "portable", "eco-friendly"},
		},
	}

	// Insert products using batch operation
	batcher := client.Batch().ObjectsBatcher()
	
	for _, product := range products {
		obj := map[string]interface{}{
			"name":        product.Name,
			"description": product.Description,
			"category":    product.Category,
			"brand":       product.Brand,
			"price":       product.Price,
			"inStock":     product.InStock,
			"tags":        product.Tags,
		}

		batcher = batcher.WithObjects(map[string]interface{}{
			"class":      "Product",
			"id":         product.ID,
			"properties": obj,
		})
	}

	// Execute batch insert
	_, err := batcher.Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to insert products: %w", err)
	}

	fmt.Printf("🛍️ Inserted %d products\n", len(products))
	return nil
}
