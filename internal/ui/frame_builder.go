package ui

import (
	"github.com/charmbracelet/glamour"
	"github.com/oxio/aia/internal/paging"
)

func buildFrame(style FrameConfig) *FrameModel {
	var contentProcessor func(string) string

	if style.IsRichText() {
		r, _ := glamour.NewTermRenderer(
			glamour.WithAutoStyle(),
			glamour.WithPreservedNewLines(),
			glamour.WithEmoji(),
			//glamour.WithWordWrap(100), // FIXME: set actual term width
		)
		contentProcessor = func(s string) string {
			out, _ := r.Render(s)
			return paging.TermTrimSuffix(out)
		}
	}

	writer := paging.NewChunkedLineWriter(
		paging.NewContentBuffer(contentProcessor),
		paging.NewChunker(20),
	)
	return NewFrame("", writer, style)
}
