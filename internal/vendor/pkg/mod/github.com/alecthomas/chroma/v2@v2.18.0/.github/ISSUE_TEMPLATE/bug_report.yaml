name: 🐞 Bug
description: File a bug/issue
title: "<title>"
labels: ["bug"]
body:
  - type: checkboxes
    attributes:
      label: Is there an existing issue for this?
      description: Please search to see if an issue already exists for the bug you encountered.
      options:
        - label: I have searched the existing issues
          required: true
  - type: textarea
    attributes:
      label: Describe the bug
      description: |
        A clear and concise description of what the bug is. Screenshots are often helpful here.

        Do *NOT* just paste a link to other issues on GitHub.
    validations:
      required: true
  - type: textarea
    attributes:
      label: To Reproduce
      description: |
        1. Reproduce your issue in the [Chroma Playground](https://swapoff.org/chroma/playground/), then click the _copy_ icon to copy a shareable link for your issue. Consider using `[markdown links](URL)` to minimise the visual noise of the links.
        2. Provide input text and a command-line invocation of `chroma` that reproduces your problem. eg. For <PERSON> this might be something like `chroma -s monokailight --html --html-lines --html-lines-table --html-inline-styles <source>`

        Do *NOT* provide configuration for another tool (eg. <PERSON>, <PERSON><PERSON><PERSON>). My time is limited and if you want me to fix your issue, help me help you.
    validations:
      required: true
