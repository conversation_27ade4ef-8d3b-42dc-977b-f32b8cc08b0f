<lexer>
  <config>
    <name>Ceylon</name>
    <alias>ceylon</alias>
    <filename>*.ceylon</filename>
    <mime_type>text/x-ceylon</mime_type>
    <dot_all>true</dot_all>
  </config>
  <rules>
    <state name="class">
      <rule pattern="[A-Za-z_]\w*">
        <token type="NameClass"/>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="import">
      <rule pattern="[a-z][\w.]*">
        <token type="NameNamespace"/>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="comment">
      <rule pattern="[^*/]">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="/\*">
        <token type="CommentMultiline"/>
        <push/>
      </rule>
      <rule pattern="\*/">
        <token type="CommentMultiline"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="[*/]">
        <token type="CommentMultiline"/>
      </rule>
    </state>
    <state name="root">
      <rule pattern="^(\s*(?:[a-zA-Z_][\w.\[\]]*\s+)+?)([a-zA-Z_]\w*)(\s*)(\()">
        <bygroups>
          <usingself state="root"/>
          <token type="NameFunction"/>
          <token type="Text"/>
          <token type="Operator"/>
        </bygroups>
      </rule>
      <rule pattern="[^\S\n]+">
        <token type="Text"/>
      </rule>
      <rule pattern="//.*?\n">
        <token type="CommentSingle"/>
      </rule>
      <rule pattern="/\*">
        <token type="CommentMultiline"/>
        <push state="comment"/>
      </rule>
      <rule pattern="(shared|abstract|formal|default|actual|variable|deprecated|small|late|literal|doc|by|see|throws|optional|license|tagged|final|native|annotation|sealed)\b">
        <token type="NameDecorator"/>
      </rule>
      <rule pattern="(break|case|catch|continue|else|finally|for|in|if|return|switch|this|throw|try|while|is|exists|dynamic|nonempty|then|outer|assert|let)\b">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(abstracts|extends|satisfies|super|given|of|out|assign)\b">
        <token type="KeywordDeclaration"/>
      </rule>
      <rule pattern="(function|value|void|new)\b">
        <token type="KeywordType"/>
      </rule>
      <rule pattern="(assembly|module|package)(\s+)">
        <bygroups>
          <token type="KeywordNamespace"/>
          <token type="Text"/>
        </bygroups>
      </rule>
      <rule pattern="(true|false|null)\b">
        <token type="KeywordConstant"/>
      </rule>
      <rule pattern="(class|interface|object|alias)(\s+)">
        <bygroups>
          <token type="KeywordDeclaration"/>
          <token type="Text"/>
        </bygroups>
        <push state="class"/>
      </rule>
      <rule pattern="(import)(\s+)">
        <bygroups>
          <token type="KeywordNamespace"/>
          <token type="Text"/>
        </bygroups>
        <push state="import"/>
      </rule>
      <rule pattern="&#34;(\\\\|\\&#34;|[^&#34;])*&#34;">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="&#39;\\.&#39;|&#39;[^\\]&#39;|&#39;\\\{#[0-9a-fA-F]{4}\}&#39;">
        <token type="LiteralStringChar"/>
      </rule>
      <rule pattern="&#34;.*``.*``.*&#34;">
        <token type="LiteralStringInterpol"/>
      </rule>
      <rule pattern="(\.)([a-z_]\w*)">
        <bygroups>
          <token type="Operator"/>
          <token type="NameAttribute"/>
        </bygroups>
      </rule>
      <rule pattern="[a-zA-Z_]\w*:">
        <token type="NameLabel"/>
      </rule>
      <rule pattern="[a-zA-Z_]\w*">
        <token type="Name"/>
      </rule>
      <rule pattern="[~^*!%&amp;\[\](){}&lt;&gt;|+=:;,./?-]">
        <token type="Operator"/>
      </rule>
      <rule pattern="\d{1,3}(_\d{3})+\.\d{1,3}(_\d{3})+[kMGTPmunpf]?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="\d{1,3}(_\d{3})+\.[0-9]+([eE][+-]?[0-9]+)?[kMGTPmunpf]?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="[0-9][0-9]*\.\d{1,3}(_\d{3})+[kMGTPmunpf]?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="[0-9][0-9]*\.[0-9]+([eE][+-]?[0-9]+)?[kMGTPmunpf]?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="#([0-9a-fA-F]{4})(_[0-9a-fA-F]{4})+">
        <token type="LiteralNumberHex"/>
      </rule>
      <rule pattern="#[0-9a-fA-F]+">
        <token type="LiteralNumberHex"/>
      </rule>
      <rule pattern="\$([01]{4})(_[01]{4})+">
        <token type="LiteralNumberBin"/>
      </rule>
      <rule pattern="\$[01]+">
        <token type="LiteralNumberBin"/>
      </rule>
      <rule pattern="\d{1,3}(_\d{3})+[kMGTP]?">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule pattern="[0-9]+[kMGTP]?">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule pattern="\n">
        <token type="Text"/>
      </rule>
    </state>
  </rules>
</lexer>