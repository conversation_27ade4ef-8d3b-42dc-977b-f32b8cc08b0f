<lexer>
  <config>
    <name>Chapel</name>
    <alias>chapel</alias>
    <alias>chpl</alias>
    <filename>*.chpl</filename>
  </config>
  <rules>
    <state name="procname">
      <rule pattern="([a-zA-Z_][.\w$]*|\~[a-zA-Z_][.\w$]*|[+*/!~%&lt;&gt;=&amp;^|\-:]{1,2})">
        <token type="NameFunction"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="\(">
        <token type="Punctuation"/>
        <push state="receivertype"/>
      </rule>
      <rule pattern="\)+\.">
        <token type="Punctuation"/>
      </rule>
    </state>
    <state name="receivertype">
      <rule pattern="(unmanaged|borrowed|atomic|single|shared|owned|sync)\b">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(complex|nothing|opaque|string|locale|bytes|range|imag|real|bool|uint|void|int)\b">
        <token type="KeywordType"/>
      </rule>
      <rule pattern="[^()]*">
        <token type="NameOther"/>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="root">
      <rule pattern="\n">
        <token type="TextWhitespace"/>
      </rule>
      <rule pattern="\s+">
        <token type="TextWhitespace"/>
      </rule>
      <rule pattern="\\\n">
        <token type="Text"/>
      </rule>
      <rule pattern="//(.*?)\n">
        <token type="CommentSingle"/>
      </rule>
      <rule pattern="/(\\\n)?[*](.|\n)*?[*](\\\n)?/">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="(config|const|inout|param|type|out|ref|var|in)\b">
        <token type="KeywordDeclaration"/>
      </rule>
      <rule pattern="(false|none|true|nil)\b">
        <token type="KeywordConstant"/>
      </rule>
      <rule pattern="(complex|nothing|opaque|string|locale|bytes|range|imag|real|bool|uint|void|int)\b">
        <token type="KeywordType"/>
      </rule>
      <rule pattern="(implements|forwarding|prototype|otherwise|subdomain|primitive|unmanaged|override|borrowed|lifetime|coforall|continue|private|require|dmapped|cobegin|foreach|lambda|sparse|shared|domain|pragma|reduce|except|export|extern|throws|forall|delete|return|noinit|single|import|select|public|inline|serial|atomic|defer|break|local|index|throw|catch|label|begin|where|while|align|yield|owned|only|this|sync|with|scan|else|enum|init|when|then|let|for|try|use|new|zip|if|by|as|on|do)\b">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(iter)(\s+)">
        <bygroups>
          <token type="Keyword"/>
          <token type="TextWhitespace"/>
        </bygroups>
        <push state="procname"/>
      </rule>
      <rule pattern="(proc)(\s+)">
        <bygroups>
          <token type="Keyword"/>
          <token type="TextWhitespace"/>
        </bygroups>
        <push state="procname"/>
      </rule>
      <rule pattern="(operator)(\s+)">
        <bygroups>
          <token type="Keyword"/>
          <token type="TextWhitespace"/>
        </bygroups>
        <push state="procname"/>
      </rule>
      <rule pattern="(class|interface|module|record|union)(\s+)">
        <bygroups>
          <token type="Keyword"/>
          <token type="TextWhitespace"/>
        </bygroups>
        <push state="classname"/>
      </rule>
      <rule pattern="\d+i">
        <token type="LiteralNumber"/>
      </rule>
      <rule pattern="\d+\.\d*([Ee][-+]\d+)?i">
        <token type="LiteralNumber"/>
      </rule>
      <rule pattern="\.\d+([Ee][-+]\d+)?i">
        <token type="LiteralNumber"/>
      </rule>
      <rule pattern="\d+[Ee][-+]\d+i">
        <token type="LiteralNumber"/>
      </rule>
      <rule pattern="(\d*\.\d+)([eE][+-]?[0-9]+)?i?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="\d+[eE][+-]?[0-9]+i?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="0[bB][01]+">
        <token type="LiteralNumberBin"/>
      </rule>
      <rule pattern="0[xX][0-9a-fA-F]+">
        <token type="LiteralNumberHex"/>
      </rule>
      <rule pattern="0[oO][0-7]+">
        <token type="LiteralNumberOct"/>
      </rule>
      <rule pattern="[0-9]+">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule pattern="&#34;(\\\\|\\&#34;|[^&#34;])*&#34;">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="&#39;(\\\\|\\&#39;|[^&#39;])*&#39;">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="(=|\+=|-=|\*=|/=|\*\*=|%=|&amp;=|\|=|\^=|&amp;&amp;=|\|\|=|&lt;&lt;=|&gt;&gt;=|&lt;=&gt;|&lt;~&gt;|\.\.|by|#|\.\.\.|&amp;&amp;|\|\||!|&amp;|\||\^|~|&lt;&lt;|&gt;&gt;|==|!=|&lt;=|&gt;=|&lt;|&gt;|[+\-*/%]|\*\*)">
        <token type="Operator"/>
      </rule>
      <rule pattern="[:;,.?()\[\]{}]">
        <token type="Punctuation"/>
      </rule>
      <rule pattern="[a-zA-Z_][\w$]*">
        <token type="NameOther"/>
      </rule>
    </state>
    <state name="classname">
      <rule pattern="[a-zA-Z_][\w$]*">
        <token type="NameClass"/>
        <pop depth="1"/>
      </rule>
    </state>
  </rules>
</lexer>