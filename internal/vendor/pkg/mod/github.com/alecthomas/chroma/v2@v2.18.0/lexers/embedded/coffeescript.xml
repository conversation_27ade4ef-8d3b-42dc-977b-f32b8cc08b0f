<lexer>
  <config>
    <name>CoffeeScript</name>
    <alias>coffee-script</alias>
    <alias>coffeescript</alias>
    <alias>coffee</alias>
    <filename>*.coffee</filename>
    <mime_type>text/coffeescript</mime_type>
    <dot_all>true</dot_all>
    <not_multiline>true</not_multiline>
  </config>
  <rules>
    <state name="commentsandwhitespace">
      <rule pattern="\s+">
        <token type="Text"/>
      </rule>
      <rule pattern="###[^#].*?###">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="#(?!##[^#]).*?\n">
        <token type="CommentSingle"/>
      </rule>
    </state>
    <state name="multilineregex">
      <rule pattern="[^/#]+">
        <token type="LiteralStringRegex"/>
      </rule>
      <rule pattern="///([gim]+\b|\B)">
        <token type="LiteralStringRegex"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="#\{">
        <token type="LiteralStringInterpol"/>
        <push state="interpoling_string"/>
      </rule>
      <rule pattern="[/#]">
        <token type="LiteralStringRegex"/>
      </rule>
    </state>
    <state name="slashstartsregex">
      <rule>
        <include state="commentsandwhitespace"/>
      </rule>
      <rule pattern="///">
        <token type="LiteralStringRegex"/>
        <push state="#pop" state="multilineregex"/>
      </rule>
      <rule pattern="/(?! )(\\.|[^[/\\\n]|\[(\\.|[^\]\\\n])*])+/([gim]+\b|\B)">
        <token type="LiteralStringRegex"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="/">
        <token type="Operator"/>
      </rule>
      <rule>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="tsqs">
      <rule pattern="&#39;&#39;&#39;">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="#|\\.|\&#39;|&#34;">
        <token type="LiteralString"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="dqs">
      <rule pattern="&#34;">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="\\.|\&#39;">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="#\{">
        <token type="LiteralStringInterpol"/>
        <push state="interpoling_string"/>
      </rule>
      <rule pattern="#">
        <token type="LiteralString"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="sqs">
      <rule pattern="&#39;">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="#|\\.|&#34;">
        <token type="LiteralString"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="tdqs">
      <rule pattern="&#34;&#34;&#34;">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="\\.|\&#39;|&#34;">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="#\{">
        <token type="LiteralStringInterpol"/>
        <push state="interpoling_string"/>
      </rule>
      <rule pattern="#">
        <token type="LiteralString"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="root">
      <rule>
        <include state="commentsandwhitespace"/>
      </rule>
      <rule pattern="^(?=\s|/)">
        <token type="Text"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="\+\+|~|&amp;&amp;|\band\b|\bor\b|\bis\b|\bisnt\b|\bnot\b|\?|:|\|\||\\(?=\n)|(&lt;&lt;|&gt;&gt;&gt;?|==?(?!&gt;)|!=?|=(?!&gt;)|-(?!&gt;)|[&lt;&gt;+*`%&amp;\|\^/])=?">
        <token type="Operator"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="(?:\([^()]*\))?\s*[=-]&gt;">
        <token type="NameFunction"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="[{(\[;,]">
        <token type="Punctuation"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="[})\].]">
        <token type="Punctuation"/>
      </rule>
      <rule pattern="(?&lt;![.$])(for|own|in|of|while|until|loop|break|return|continue|switch|when|then|if|unless|else|throw|try|catch|finally|new|delete|typeof|instanceof|super|extends|this|class|by)\b">
        <token type="Keyword"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="(?&lt;![.$])(true|false|yes|no|on|off|null|NaN|Infinity|undefined)\b">
        <token type="KeywordConstant"/>
      </rule>
      <rule pattern="(Array|Boolean|Date|Error|Function|Math|netscape|Number|Object|Packages|RegExp|String|sun|decodeURI|decodeURIComponent|encodeURI|encodeURIComponent|eval|isFinite|isNaN|parseFloat|parseInt|document|window)\b">
        <token type="NameBuiltin"/>
      </rule>
      <rule pattern="[$a-zA-Z_][\w.:$]*\s*[:=]\s">
        <token type="NameVariable"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="@[$a-zA-Z_][\w.:$]*\s*[:=]\s">
        <token type="NameVariableInstance"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="@">
        <token type="NameOther"/>
        <push state="slashstartsregex"/>
      </rule>
      <rule pattern="@?[$a-zA-Z_][\w$]*">
        <token type="NameOther"/>
      </rule>
      <rule pattern="[0-9][0-9]*\.[0-9]+([eE][0-9]+)?[fd]?">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="0x[0-9a-fA-F]+">
        <token type="LiteralNumberHex"/>
      </rule>
      <rule pattern="[0-9]+">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule pattern="&#34;&#34;&#34;">
        <token type="LiteralString"/>
        <push state="tdqs"/>
      </rule>
      <rule pattern="&#39;&#39;&#39;">
        <token type="LiteralString"/>
        <push state="tsqs"/>
      </rule>
      <rule pattern="&#34;">
        <token type="LiteralString"/>
        <push state="dqs"/>
      </rule>
      <rule pattern="&#39;">
        <token type="LiteralString"/>
        <push state="sqs"/>
      </rule>
    </state>
    <state name="interpoling_string">
      <rule pattern="\}">
        <token type="LiteralStringInterpol"/>
        <pop depth="1"/>
      </rule>
      <rule>
        <include state="root"/>
      </rule>
    </state>
    <state name="strings">
      <rule pattern="[^#\\\&#39;&#34;]+">
        <token type="LiteralString"/>
      </rule>
    </state>
  </rules>
</lexer>