<lexer>
  <config>
    <name>Mojo</name>
    <alias>mojo</alias>
    <alias>🔥</alias>
    <filename>*.mojo</filename>
    <filename>*.🔥</filename>
    <mime_type>text/x-mojo</mime_type>
    <mime_type>application/x-mojo</mime_type>
  </config>
  <rules>
    <state name="root">
      <rule pattern="\s+"><token type="TextWhitespace"/></rule>
      <rule pattern="^(\s*)([rRuUbB]{,2})(&quot;&quot;&quot;(?:.|\n)*?&quot;&quot;&quot;)"><bygroups><token type="TextWhitespace"/><token type="LiteralStringAffix"/><token type="LiteralStringDoc"/></bygroups></rule>
      <rule pattern="^(\s*)([rRuUbB]{,2})(&#x27;&#x27;&#x27;(?:.|\n)*?&#x27;&#x27;&#x27;)"><bygroups><token type="TextWhitespace"/><token type="LiteralStringAffix"/><token type="LiteralStringDoc"/></bygroups></rule>
      <rule pattern="\A#!.+$"><token type="CommentHashbang"/></rule>
      <rule pattern="#.*$"><token type="CommentSingle"/></rule>
      <rule pattern="\\\n"><token type="TextWhitespace"/></rule>
      <rule pattern="\\"><token type="TextWhitespace"/></rule>
      <rule><include state="keywords"/></rule>
      <rule><include state="soft-keywords"/></rule>
      <rule pattern="(alias)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="varname"/></rule>
      <rule pattern="(var)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="varname"/></rule>
      <rule pattern="(def)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="funcname"/></rule>
      <rule pattern="(fn)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="funcname"/></rule>
      <rule pattern="(class)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="classname"/></rule>
      <rule pattern="(struct)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="structname"/></rule>
      <rule pattern="(trait)(\s+)"><bygroups><token type="Keyword"/><token type="TextWhitespace"/></bygroups><push state="structname"/></rule>
      <rule pattern="(from)(\s+)"><bygroups><token type="KeywordNamespace"/><token type="TextWhitespace"/></bygroups><push state="fromimport"/></rule>
      <rule pattern="(import)(\s+)"><bygroups><token type="KeywordNamespace"/><token type="TextWhitespace"/></bygroups><push state="import"/></rule>
      <rule><include state="expr"/></rule>
    </state>
    <state name="expr">
      <rule pattern="(?i)(rf|fr)(&quot;&quot;&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="rfstringescape" state="tdqf"/></rule>
      <rule pattern="(?i)(rf|fr)(&#x27;&#x27;&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="rfstringescape" state="tsqf"/></rule>
      <rule pattern="(?i)(rf|fr)(&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="rfstringescape" state="dqf"/></rule>
      <rule pattern="(?i)(rf|fr)(&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="rfstringescape" state="sqf"/></rule>
      <rule pattern="([fF])(&quot;&quot;&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="fstringescape" state="tdqf"/></rule>
      <rule pattern="([fF])(&#x27;&#x27;&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="fstringescape" state="tsqf"/></rule>
      <rule pattern="([fF])(&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="fstringescape" state="dqf"/></rule>
      <rule pattern="([fF])(&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="fstringescape" state="sqf"/></rule>
      <rule pattern="(?i)(rb|br|r)(&quot;&quot;&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><push state="tdqs"/></rule>
      <rule pattern="(?i)(rb|br|r)(&#x27;&#x27;&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><push state="tsqs"/></rule>
      <rule pattern="(?i)(rb|br|r)(&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><push state="dqs"/></rule>
      <rule pattern="(?i)(rb|br|r)(&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><push state="sqs"/></rule>
      <rule pattern="([uU]?)(&quot;&quot;&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="stringescape" state="tdqs"/></rule>
      <rule pattern="([uU]?)(&#x27;&#x27;&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="stringescape" state="tsqs"/></rule>
      <rule pattern="([uU]?)(&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="stringescape" state="dqs"/></rule>
      <rule pattern="([uU]?)(&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="stringescape" state="sqs"/></rule>
      <rule pattern="([bB])(&quot;&quot;&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="bytesescape" state="tdqs"/></rule>
      <rule pattern="([bB])(&#x27;&#x27;&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="bytesescape" state="tsqs"/></rule>
      <rule pattern="([bB])(&quot;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringDouble"/></bygroups><combined state="bytesescape" state="dqs"/></rule>
      <rule pattern="([bB])(&#x27;)"><bygroups><token type="LiteralStringAffix"/><token type="LiteralStringSingle"/></bygroups><combined state="bytesescape" state="sqs"/></rule>
      <rule pattern="[^\S\n]+"><token type="Text"/></rule>
      <rule><include state="numbers"/></rule>
      <rule pattern="!=|==|&lt;&lt;|&gt;&gt;|:=|[-~+/*%=&lt;&gt;&amp;^|.]"><token type="Operator"/></rule>
      <rule pattern="([]{}:\(\),;[])+"><token type="Punctuation"/></rule>
      <rule pattern="(in|is|and|or|not)\b"><token type="OperatorWord"/></rule>
      <rule><include state="expr-keywords"/></rule>
      <rule><include state="builtins"/></rule>
      <rule><include state="magicfuncs"/></rule>
      <rule><include state="magicvars"/></rule>
      <rule><include state="name"/></rule>
    </state>
    <state name="expr-inside-fstring">
      <rule pattern="[{([]"><token type="Punctuation"/><push state="expr-inside-fstring-inner"/></rule>
      <rule pattern="(=\s*)?(\![sraf])?\}"><token type="LiteralStringInterpol"/><pop depth="1"/></rule>
      <rule pattern="(=\s*)?(\![sraf])?:"><token type="LiteralStringInterpol"/><pop depth="1"/></rule>
      <rule pattern="\s+"><token type="TextWhitespace"/></rule>
      <rule><include state="expr"/></rule>
    </state>
    <state name="expr-inside-fstring-inner">
      <rule pattern="[{([]"><token type="Punctuation"/><push state="expr-inside-fstring-inner"/></rule>
      <rule pattern="[])}]"><token type="Punctuation"/><pop depth="1"/></rule>
      <rule pattern="\s+"><token type="TextWhitespace"/></rule>
      <rule><include state="expr"/></rule>
    </state>
    <state name="expr-keywords">
      <rule pattern="(async\ for|async\ with|await|else|for|if|lambda|yield|yield\ from)\b"><token type="Keyword"/></rule>
      <rule pattern="(True|False|None)\b"><token type="KeywordConstant"/></rule>
    </state>
    <state name="keywords">
      <rule pattern="(assert|async|await|borrowed|break|continue|del|elif|else|except|finally|for|global|if|lambda|pass|raise|nonlocal|return|try|while|yield|yield\ from|as|with)\b"><token type="Keyword"/></rule>
      <rule pattern="(True|False|None)\b"><token type="KeywordConstant"/></rule>
    </state>
    <state name="soft-keywords">
      <rule pattern="(^[ \t]*)(match|case)\b(?![ \t]*(?:[:,;=^&amp;|@~)\]}]|(?:and|as|assert|async|await|break|class|continue|def|del|elif|else|except|finally|for|from|global|if|import|in|is|lambda|nonlocal|not|or|pass|raise|return|try|while|with|yield)\b))"><bygroups><token type="TextWhitespace"/><token type="Keyword"/></bygroups><push state="soft-keywords-inner"/></rule>
    </state>
    <state name="soft-keywords-inner">
      <rule pattern="(\s+)([^\n_]*)(_\b)"><bygroups><token type="TextWhitespace"/><usingself state="root"/><token type="Keyword"/></bygroups></rule>
      <rule><pop depth="1"/></rule>
    </state>
    <state name="builtins">
      <rule pattern="(?&lt;!\.)(__import__|abs|aiter|all|any|bin|bool|bytearray|breakpoint|bytes|callable|chr|classmethod|compile|complex|delattr|dict|dir|divmod|enumerate|eval|filter|float|format|frozenset|getattr|globals|hasattr|hash|hex|id|input|int|isinstance|issubclass|iter|len|list|locals|map|max|memoryview|min|next|object|oct|open|ord|pow|print|property|range|repr|reversed|round|set|setattr|slice|sorted|staticmethod|str|sum|super|tuple|type|vars|zip|AnyType|Coroutine|DType|Error|Int|List|ListLiteral|Scalar|Int8|UInt8|Int16|UInt16|Int32|UInt32|Int64|UInt64|BFloat16|Float16|Float32|Float64|SIMD|String|Tensor|Tuple|Movable|Copyable|CollectionElement)\b"><token type="NameBuiltin"/></rule>
      <rule pattern="(?&lt;!\.)(self|Ellipsis|NotImplemented|cls)\b"><token type="NameBuiltinPseudo"/></rule>
      <rule pattern="(?&lt;!\.)(Error)\b"><token type="NameException"/></rule>
    </state>
    <state name="magicfuncs">
      <rule pattern="(__abs__|__add__|__aenter__|__aexit__|__aiter__|__and__|__anext__|__await__|__bool__|__bytes__|__call__|__complex__|__contains__|__del__|__delattr__|__delete__|__delitem__|__dir__|__divmod__|__enter__|__eq__|__exit__|__float__|__floordiv__|__format__|__ge__|__get__|__getattr__|__getattribute__|__getitem__|__gt__|__hash__|__iadd__|__iand__|__ifloordiv__|__ilshift__|__imatmul__|__imod__|__imul__|__index__|__init__|__instancecheck__|__int__|__invert__|__ior__|__ipow__|__irshift__|__isub__|__iter__|__itruediv__|__ixor__|__le__|__len__|__length_hint__|__lshift__|__lt__|__matmul__|__missing__|__mod__|__mul__|__ne__|__neg__|__new__|__next__|__or__|__pos__|__pow__|__prepare__|__radd__|__rand__|__rdivmod__|__repr__|__reversed__|__rfloordiv__|__rlshift__|__rmatmul__|__rmod__|__rmul__|__ror__|__round__|__rpow__|__rrshift__|__rshift__|__rsub__|__rtruediv__|__rxor__|__set__|__setattr__|__setitem__|__str__|__sub__|__subclasscheck__|__truediv__|__xor__)\b"><token type="NameFunctionMagic"/></rule>
    </state>
    <state name="magicvars">
      <rule pattern="(__annotations__|__bases__|__class__|__closure__|__code__|__defaults__|__dict__|__doc__|__file__|__func__|__globals__|__kwdefaults__|__module__|__mro__|__name__|__objclass__|__qualname__|__self__|__slots__|__weakref__)\b"><token type="NameVariableMagic"/></rule>
    </state>
    <state name="numbers">
      <rule pattern="(\d(?:_?\d)*\.(?:\d(?:_?\d)*)?|(?:\d(?:_?\d)*)?\.\d(?:_?\d)*)([eE][+-]?\d(?:_?\d)*)?"><token type="LiteralNumberFloat"/></rule>
      <rule pattern="\d(?:_?\d)*[eE][+-]?\d(?:_?\d)*j?"><token type="LiteralNumberFloat"/></rule>
      <rule pattern="0[oO](?:_?[0-7])+"><token type="LiteralNumberOct"/></rule>
      <rule pattern="0[bB](?:_?[01])+"><token type="LiteralNumberBin"/></rule>
      <rule pattern="0[xX](?:_?[a-fA-F0-9])+"><token type="LiteralNumberHex"/></rule>
      <rule pattern="\d(?:_?\d)*"><token type="LiteralNumberInteger"/></rule>
    </state>
    <state name="name">
      <rule pattern="@[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameDecorator"/></rule>
      <rule pattern="@"><token type="Operator"/></rule>
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="Name"/></rule>
    </state>
    <state name="varname">
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameVariable"/><pop depth="1"/></rule>
    </state>
    <state name="funcname">
      <rule><include state="magicfuncs"/></rule>
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameFunction"/><pop depth="1"/></rule>
      <rule><pop depth="1"/></rule>
    </state>
    <state name="classname">
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameClass"/><pop depth="1"/></rule>
    </state>
    <state name="structname">
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameClass"/><pop depth="1"/></rule>
    </state>
    <state name="import">
      <rule pattern="(\s+)(as)(\s+)"><bygroups><token type="TextWhitespace"/><token type="Keyword"/><token type="TextWhitespace"/></bygroups></rule>
      <rule pattern="\."><token type="NameNamespace"/></rule>
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameNamespace"/></rule>
      <rule pattern="(\s*)(,)(\s*)"><bygroups><token type="TextWhitespace"/><token type="Operator"/><token type="TextWhitespace"/></bygroups></rule>
      <rule><pop depth="1"/></rule>
    </state>
    <state name="fromimport">
      <rule pattern="(\s+)(import)\b"><bygroups><token type="TextWhitespace"/><token type="KeywordNamespace"/></bygroups><pop depth="1"/></rule>
      <rule pattern="\."><token type="NameNamespace"/></rule>
      <rule pattern="None\b"><token type="KeywordConstant"/><pop depth="1"/></rule>
      <rule pattern="[_\p{L}][_\p{L}\p{N}]*(\s*\.\s*[_\p{L}][_\p{L}\p{N}]*)*"><token type="NameNamespace"/></rule>
      <rule><pop depth="1"/></rule>
    </state>
    <state name="rfstringescape">
      <rule pattern="\{\{"><token type="LiteralStringEscape"/></rule>
      <rule pattern="\}\}"><token type="LiteralStringEscape"/></rule>
    </state>
    <state name="fstringescape">
      <rule><include state="rfstringescape"/></rule>
      <rule><include state="stringescape"/></rule>
    </state>
    <state name="bytesescape">
      <rule pattern="\\([\\abfnrtv&quot;\&#x27;]|\n|x[a-fA-F0-9]{2}|[0-7]{1,3})"><token type="LiteralStringEscape"/></rule>
    </state>
    <state name="stringescape">
      <rule pattern="\\(N\{.*?\}|u[a-fA-F0-9]{4}|U[a-fA-F0-9]{8})"><token type="LiteralStringEscape"/></rule>
      <rule><include state="bytesescape"/></rule>
    </state>
    <state name="fstrings-single">
      <rule pattern="\}"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="\{"><token type="LiteralStringInterpol"/><push state="expr-inside-fstring"/></rule>
      <rule pattern="[^\\\&#x27;&quot;{}\n]+"><token type="LiteralStringSingle"/></rule>
      <rule pattern="[\&#x27;&quot;\\]"><token type="LiteralStringSingle"/></rule>
    </state>
    <state name="fstrings-double">
      <rule pattern="\}"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="\{"><token type="LiteralStringInterpol"/><push state="expr-inside-fstring"/></rule>
      <rule pattern="[^\\\&#x27;&quot;{}\n]+"><token type="LiteralStringDouble"/></rule>
      <rule pattern="[\&#x27;&quot;\\]"><token type="LiteralStringDouble"/></rule>
    </state>
    <state name="strings-single">
      <rule pattern="%(\(\w+\))?[-#0 +]*([0-9]+|[*])?(\.([0-9]+|[*]))?[hlL]?[E-GXc-giorsaux%]"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="\{((\w+)((\.\w+)|(\[[^\]]+\]))*)?(\![sra])?(\:(.?[&lt;&gt;=\^])?[-+ ]?#?0?(\d+)?,?(\.\d+)?[E-GXb-gnosx%]?)?\}"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="[^\\\&#x27;&quot;%{\n]+"><token type="LiteralStringSingle"/></rule>
      <rule pattern="[\&#x27;&quot;\\]"><token type="LiteralStringSingle"/></rule>
      <rule pattern="%|(\{{1,2})"><token type="LiteralStringSingle"/></rule>
    </state>
    <state name="strings-double">
      <rule pattern="%(\(\w+\))?[-#0 +]*([0-9]+|[*])?(\.([0-9]+|[*]))?[hlL]?[E-GXc-giorsaux%]"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="\{((\w+)((\.\w+)|(\[[^\]]+\]))*)?(\![sra])?(\:(.?[&lt;&gt;=\^])?[-+ ]?#?0?(\d+)?,?(\.\d+)?[E-GXb-gnosx%]?)?\}"><token type="LiteralStringInterpol"/></rule>
      <rule pattern="[^\\\&#x27;&quot;%{\n]+"><token type="LiteralStringDouble"/></rule>
      <rule pattern="[\&#x27;&quot;\\]"><token type="LiteralStringDouble"/></rule>
      <rule pattern="%|(\{{1,2})"><token type="LiteralStringDouble"/></rule>
    </state>
    <state name="dqf">
      <rule pattern="&quot;"><token type="LiteralStringDouble"/><pop depth="1"/></rule>
      <rule pattern="\\\\|\\&quot;|\\\n"><token type="LiteralStringEscape"/></rule>
      <rule><include state="fstrings-double"/></rule>
    </state>
    <state name="sqf">
      <rule pattern="&#x27;"><token type="LiteralStringSingle"/><pop depth="1"/></rule>
      <rule pattern="\\\\|\\&#x27;|\\\n"><token type="LiteralStringEscape"/></rule>
      <rule><include state="fstrings-single"/></rule>
    </state>
    <state name="dqs">
      <rule pattern="&quot;"><token type="LiteralStringDouble"/><pop depth="1"/></rule>
      <rule pattern="\\\\|\\&quot;|\\\n"><token type="LiteralStringEscape"/></rule>
      <rule><include state="strings-double"/></rule>
    </state>
    <state name="sqs">
      <rule pattern="&#x27;"><token type="LiteralStringSingle"/><pop depth="1"/></rule>
      <rule pattern="\\\\|\\&#x27;|\\\n"><token type="LiteralStringEscape"/></rule>
      <rule><include state="strings-single"/></rule>
    </state>
    <state name="tdqf">
      <rule pattern="&quot;&quot;&quot;"><token type="LiteralStringDouble"/><pop depth="1"/></rule>
      <rule><include state="fstrings-double"/></rule>
      <rule pattern="\n"><token type="LiteralStringDouble"/></rule>
    </state>
    <state name="tsqf">
      <rule pattern="&#x27;&#x27;&#x27;"><token type="LiteralStringSingle"/><pop depth="1"/></rule>
      <rule><include state="fstrings-single"/></rule>
      <rule pattern="\n"><token type="LiteralStringSingle"/></rule>
    </state>
    <state name="tdqs">
      <rule pattern="&quot;&quot;&quot;"><token type="LiteralStringDouble"/><pop depth="1"/></rule>
      <rule><include state="strings-double"/></rule>
      <rule pattern="\n"><token type="LiteralStringDouble"/></rule>
    </state>
    <state name="tsqs">
      <rule pattern="&#x27;&#x27;&#x27;"><token type="LiteralStringSingle"/><pop depth="1"/></rule>
      <rule><include state="strings-single"/></rule>
      <rule pattern="\n"><token type="LiteralStringSingle"/></rule>
    </state>
  </rules>
</lexer>

