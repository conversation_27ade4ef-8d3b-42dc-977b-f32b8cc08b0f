<lexer>
  <config>
    <name>Nim</name>
    <alias>nim</alias>
    <alias>nimrod</alias>
    <filename>*.nim</filename>
    <filename>*.nimrod</filename>
    <mime_type>text/x-nim</mime_type>
    <case_insensitive>true</case_insensitive>
  </config>
  <rules>
    <state name="dqs">
      <rule pattern="\\([\\abcefnrtvl&#34;\&#39;]|\n|x[a-f0-9]{2}|[0-9]{1,3})">
        <token type="LiteralStringEscape"/>
      </rule>
      <rule pattern="&#34;">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="tdqs">
      <rule pattern="&#34;&#34;&#34;(?!&#34;)">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
      <rule>
        <include state="nl"/>
      </rule>
    </state>
    <state name="funcname">
      <rule pattern="((?![\d_])\w)(((?!_)\w)|(_(?!_)\w))*">
        <token type="NameFunction"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="`.+`">
        <token type="NameFunction"/>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="int-suffix">
      <rule pattern="\&#39;(i|u)(32|64)">
        <token type="LiteralNumberIntegerLong"/>
      </rule>
      <rule pattern="\&#39;(u|(i|u)(8|16))">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="float-suffix">
      <rule pattern="\&#39;(f|d|f(32|64))">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="strings">
      <rule pattern="(?&lt;!\$)\$(\d+|#|\w+)+">
        <token type="LiteralStringInterpol"/>
      </rule>
      <rule pattern="[^\\\&#39;&#34;$\n]+">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="[\&#39;&#34;\\]">
        <token type="LiteralString"/>
      </rule>
      <rule pattern="\$">
        <token type="LiteralString"/>
      </rule>
    </state>
    <state name="nl">
      <rule pattern="\n">
        <token type="LiteralString"/>
      </rule>
    </state>
    <state name="chars">
      <rule pattern="\\([\\abcefnrtvl&#34;\&#39;]|x[a-f0-9]{2}|[0-9]{1,3})">
        <token type="LiteralStringEscape"/>
      </rule>
      <rule pattern="&#39;">
        <token type="LiteralStringChar"/>
        <pop depth="1"/>
      </rule>
      <rule pattern=".">
        <token type="LiteralStringChar"/>
      </rule>
    </state>
    <state name="rdqs">
      <rule pattern="&#34;(?!&#34;)">
        <token type="LiteralString"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="&#34;&#34;">
        <token type="LiteralStringEscape"/>
      </rule>
      <rule>
        <include state="strings"/>
      </rule>
    </state>
    <state name="float-number">
      <rule pattern="\.(?!\.)[0-9_]*">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="e[+-]?[0-9][0-9_]*">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule>
        <pop depth="1"/>
      </rule>
    </state>
    <state name="root">
      <rule pattern="#\[[\s\S]*?\]#">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="##.*$">
        <token type="LiteralStringDoc"/>
      </rule>
      <rule pattern="#.*$">
        <token type="Comment"/>
      </rule>
      <rule pattern="[*=&gt;&lt;+\-/@$~&amp;%!?|\\\[\]]">
        <token type="Operator"/>
      </rule>
      <rule pattern="\.\.|\.|,|\[\.|\.\]|\{\.|\.\}|\(\.|\.\)|\{|\}|\(|\)|:|\^|`|;">
        <token type="Punctuation"/>
      </rule>
      <rule pattern="(?:[\w]+)&#34;&#34;&#34;">
        <token type="LiteralString"/>
        <push state="tdqs"/>
      </rule>
      <rule pattern="(?:[\w]+)&#34;">
        <token type="LiteralString"/>
        <push state="rdqs"/>
      </rule>
      <rule pattern="&#34;&#34;&#34;">
        <token type="LiteralString"/>
        <push state="tdqs"/>
      </rule>
      <rule pattern="&#34;">
        <token type="LiteralString"/>
        <push state="dqs"/>
      </rule>
      <rule pattern="&#39;">
        <token type="LiteralStringChar"/>
        <push state="chars"/>
      </rule>
      <rule pattern="(a_?n_?d_?|o_?r_?|n_?o_?t_?|x_?o_?r_?|s_?h_?l_?|s_?h_?r_?|d_?i_?v_?|m_?o_?d_?|i_?n_?|n_?o_?t_?i_?n_?|i_?s_?|i_?s_?n_?o_?t_?)\b">
        <token type="OperatorWord"/>
      </rule>
      <rule pattern="(p_?r_?o_?c_?\s)(?![(\[\]])">
        <token type="Keyword"/>
        <push state="funcname"/>
      </rule>
      <rule pattern="(a_?d_?d_?r_?|a_?n_?d_?|a_?s_?|a_?s_?m_?|a_?t_?o_?m_?i_?c_?|b_?i_?n_?d_?|b_?l_?o_?c_?k_?|b_?r_?e_?a_?k_?|c_?a_?s_?e_?|c_?a_?s_?t_?|c_?o_?n_?c_?e_?p_?t_?|c_?o_?n_?s_?t_?|c_?o_?n_?t_?i_?n_?u_?e_?|c_?o_?n_?v_?e_?r_?t_?e_?r_?|d_?e_?f_?e_?r_?|d_?i_?s_?c_?a_?r_?d_?|d_?i_?s_?t_?i_?n_?c_?t_?|d_?i_?v_?|d_?o_?|e_?l_?i_?f_?|e_?l_?s_?e_?|e_?n_?d_?|e_?n_?u_?m_?|e_?x_?c_?e_?p_?t_?|e_?x_?p_?o_?r_?t_?|f_?i_?n_?a_?l_?l_?y_?|f_?o_?r_?|f_?u_?n_?c_?|i_?f_?|i_?n_?|y_?i_?e_?l_?d_?|i_?n_?t_?e_?r_?f_?a_?c_?e_?|i_?s_?|i_?s_?n_?o_?t_?|i_?t_?e_?r_?a_?t_?o_?r_?|l_?e_?t_?|m_?a_?c_?r_?o_?|m_?e_?t_?h_?o_?d_?|m_?i_?x_?i_?n_?|m_?o_?d_?|n_?o_?t_?|n_?o_?t_?i_?n_?|o_?b_?j_?e_?c_?t_?|o_?f_?|o_?r_?|o_?u_?t_?|p_?r_?o_?c_?|p_?t_?r_?|r_?a_?i_?s_?e_?|r_?e_?f_?|r_?e_?t_?u_?r_?n_?|s_?h_?a_?r_?e_?d_?|s_?h_?l_?|s_?h_?r_?|s_?t_?a_?t_?i_?c_?|t_?e_?m_?p_?l_?a_?t_?e_?|t_?r_?y_?|t_?u_?p_?l_?e_?|t_?y_?p_?e_?|w_?h_?e_?n_?|w_?h_?i_?l_?e_?|w_?i_?t_?h_?|w_?i_?t_?h_?o_?u_?t_?|x_?o_?r_?)\b">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(f_?r_?o_?m_?|i_?m_?p_?o_?r_?t_?|i_?n_?c_?l_?u_?d_?e_?)\b">
        <token type="KeywordNamespace"/>
      </rule>
      <rule pattern="(v_?a_?r)\b">
        <token type="KeywordDeclaration"/>
      </rule>
      <rule pattern="(i_?n_?t_?|i_?n_?t_?8_?|i_?n_?t_?1_?6_?|i_?n_?t_?3_?2_?|i_?n_?t_?6_?4_?|f_?l_?o_?a_?t_?|f_?l_?o_?a_?t_?3_?2_?|f_?l_?o_?a_?t_?6_?4_?|b_?o_?o_?l_?|c_?h_?a_?r_?|r_?a_?n_?g_?e_?|a_?r_?r_?a_?y_?|s_?e_?q_?|s_?e_?t_?|s_?t_?r_?i_?n_?g_?)\b">
        <token type="KeywordType"/>
      </rule>
      <rule pattern="(n_?i_?l_?|t_?r_?u_?e_?|f_?a_?l_?s_?e_?)\b">
        <token type="KeywordPseudo"/>
      </rule>
      <rule pattern="\b_\b">
        <token type="Name"/>
      </rule>
      <rule pattern="\b((?![_\d])\w)(((?!_)\w)|(_(?!_)\w))*">
        <token type="Name"/>
      </rule>
      <rule pattern="[0-9][0-9_]*(?=([e.]|\&#39;(f|d|f(32|64))))">
        <token type="LiteralNumberFloat"/>
        <push state="float-suffix" state="float-number"/>
      </rule>
      <rule pattern="0x[a-f0-9][a-f0-9_]*">
        <token type="LiteralNumberHex"/>
        <push state="int-suffix"/>
      </rule>
      <rule pattern="0b[01][01_]*">
        <token type="LiteralNumberBin"/>
        <push state="int-suffix"/>
      </rule>
      <rule pattern="0o[0-7][0-7_]*">
        <token type="LiteralNumberOct"/>
        <push state="int-suffix"/>
      </rule>
      <rule pattern="[0-9][0-9_]*">
        <token type="LiteralNumberInteger"/>
        <push state="int-suffix"/>
      </rule>
      <rule pattern="\s+">
        <token type="Text"/>
      </rule>
      <rule pattern=".+$">
        <token type="Error"/>
      </rule>
    </state>
  </rules>
</lexer>