<lexer>
  <config>
    <name>OpenSCAD</name>
    <alias>openscad</alias>
    <filename>*.scad</filename>
    <mime_type>text/x-scad</mime_type>
  </config>
  <rules>
    <state name="root">
      <rule pattern="[^\S\n]+">
        <token type="Text"/>
      </rule>
      <rule pattern="\n">
        <token type="Text"/>
      </rule>
      <rule pattern="//(\n|[\w\W]*?[^\\]\n)">
        <token type="CommentSingle"/>
      </rule>
      <rule pattern="/(\\\n)?[*][\w\W]*?[*](\\\n)?/">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="/(\\\n)?[*][\w\W]*">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="[{}\[\]\(\),;:]">
        <token type="Punctuation"/>
      </rule>
      <rule pattern="[*!#%\-+=?/]">
        <token type="Operator"/>
      </rule>
      <rule pattern="&lt;|&lt;=|==|!=|&gt;=|&gt;|&amp;&amp;|\|\|">
        <token type="Operator"/>
      </rule>
      <rule pattern="\$(f[asn]|t|vp[rtd]|children)">
        <token type="NameVariableMagic"/>
      </rule>
      <rule pattern="(undef|PI)\b">
        <token type="KeywordConstant"/>
      </rule>
      <rule pattern="(use|include)((?:\s|\\\\s)+)">
        <bygroups>
          <token type="KeywordNamespace"/>
          <token type="Text"/>
        </bygroups>
        <push state="includes"/>
      </rule>
      <rule pattern="(module)(\s*)([^\s\(]+)">
        <bygroups>
          <token type="KeywordNamespace"/>
          <token type="Text"/>
          <token type="NameNamespace"/>
        </bygroups>
      </rule>
      <rule pattern="(function)(\s*)([^\s\(]+)">
        <bygroups>
          <token type="KeywordDeclaration"/>
          <token type="Text"/>
          <token type="NameFunction"/>
        </bygroups>
      </rule>
      <rule pattern="\b(true|false)\b">
        <token type="Literal"/>
      </rule>
      <rule pattern="\b(function|module|include|use|for|intersection_for|if|else|return)\b">
        <token type="Keyword"/>
      </rule>
      <rule pattern="\b(circle|square|polygon|text|sphere|cube|cylinder|polyhedron|translate|rotate|scale|resize|mirror|multmatrix|color|offset|hull|minkowski|union|difference|intersection|abs|sign|sin|cos|tan|acos|asin|atan|atan2|floor|round|ceil|ln|log|pow|sqrt|exp|rands|min|max|concat|lookup|str|chr|search|version|version_num|norm|cross|parent_module|echo|import|import_dxf|dxf_linear_extrude|linear_extrude|rotate_extrude|surface|projection|render|dxf_cross|dxf_dim|let|assign|len)\b">
        <token type="NameBuiltin"/>
      </rule>
      <rule pattern="\bchildren\b">
        <token type="NameBuiltinPseudo"/>
      </rule>
      <rule pattern="&#34;(\\\\|\\&#34;|[^&#34;])*&#34;">
        <token type="LiteralStringDouble"/>
      </rule>
      <rule pattern="-?\d+(\.\d+)?(e[+-]?\d+)?">
        <token type="LiteralNumber"/>
      </rule>
      <rule pattern="[a-zA-Z_]\w*">
        <token type="Name"/>
      </rule>
    </state>
    <state name="includes">
      <rule pattern="(&lt;)([^&gt;]*)(&gt;)">
        <bygroups>
          <token type="Punctuation"/>
          <token type="CommentPreprocFile"/>
          <token type="Punctuation"/>
        </bygroups>
      </rule>
      <rule>
        <pop depth="1"/>
      </rule>
    </state>
  </rules>
</lexer>