
<lexer>
  <config>
    <name>Vala</name>
    <alias>vala</alias>
    <alias>vapi</alias>
    <filename>*.vala</filename>
    <filename>*.vapi</filename>
    <mime_type>text/x-vala</mime_type>
  </config>
  <rules>
    <state name="whitespace">
      <rule pattern="^\s*#if\s+0"><token type="CommentPreproc"/><push state="if0"/></rule>
      <rule pattern="\n"><token type="TextWhitespace"/></rule>
      <rule pattern="\s+"><token type="TextWhitespace"/></rule>
      <rule pattern="\\\n"><token type="Text"/></rule>
      <rule pattern="//(\n|(.|\n)*?[^\\]\n)"><token type="CommentSingle"/></rule>
      <rule pattern="/(\\\n)?[*](.|\n)*?[*](\\\n)?/"><token type="CommentMultiline"/></rule>
    </state>
    <state name="statements">
      <rule pattern="[L@]?&quot;"><token type="LiteralString"/><push state="string"/></rule>
      <rule pattern="L?&#x27;(\\.|\\[0-7]{1,3}|\\x[a-fA-F0-9]{1,2}|[^\\\&#x27;\n])&#x27;"><token type="LiteralStringChar"/></rule>
      <rule pattern="(?s)&quot;&quot;&quot;.*?&quot;&quot;&quot;"><token type="LiteralString"/></rule>
      <rule pattern="(\d+\.\d*|\.\d+|\d+)[eE][+-]?\d+[lL]?"><token type="LiteralNumberFloat"/></rule>
      <rule pattern="(\d+\.\d*|\.\d+|\d+[fF])[fF]?"><token type="LiteralNumberFloat"/></rule>
      <rule pattern="0x[0-9a-fA-F]+[Ll]?"><token type="LiteralNumberHex"/></rule>
      <rule pattern="0[0-7]+[Ll]?"><token type="LiteralNumberOct"/></rule>
      <rule pattern="\d+[Ll]?"><token type="LiteralNumberInteger"/></rule>
      <rule pattern="[~!%^&amp;*+=|?:&lt;&gt;/-]"><token type="Operator"/></rule>
      <rule pattern="(\[)(Compact|Immutable|(?:Boolean|Simple)Type)(\])"><bygroups><token type="Punctuation"/><token type="NameDecorator"/><token type="Punctuation"/></bygroups></rule>
      <rule pattern="(\[)(CCode|(?:Integer|Floating)Type)"><bygroups><token type="Punctuation"/><token type="NameDecorator"/></bygroups></rule>
      <rule pattern="[()\[\],.]"><token type="Punctuation"/></rule>
      <rule pattern="(as|base|break|case|catch|construct|continue|default|delete|do|else|enum|finally|for|foreach|get|if|in|is|lock|new|out|params|return|set|sizeof|switch|this|throw|try|typeof|while|yield)\b"><token type="Keyword"/></rule>
      <rule pattern="(abstract|const|delegate|dynamic|ensures|extern|inline|internal|override|owned|private|protected|public|ref|requires|signal|static|throws|unowned|var|virtual|volatile|weak|yields)\b"><token type="KeywordDeclaration"/></rule>
      <rule pattern="(namespace|using)(\s+)"><bygroups><token type="KeywordNamespace"/><token type="TextWhitespace"/></bygroups><push state="namespace"/></rule>
      <rule pattern="(class|errordomain|interface|struct)(\s+)"><bygroups><token type="KeywordDeclaration"/><token type="TextWhitespace"/></bygroups><push state="class"/></rule>
      <rule pattern="(\.)([a-zA-Z_]\w*)"><bygroups><token type="Operator"/><token type="NameAttribute"/></bygroups></rule>
      <rule pattern="(void|bool|char|double|float|int|int8|int16|int32|int64|long|short|size_t|ssize_t|string|time_t|uchar|uint|uint8|uint16|uint32|uint64|ulong|unichar|ushort)\b"><token type="KeywordType"/></rule>
      <rule pattern="(true|false|null)\b"><token type="NameBuiltin"/></rule>
      <rule pattern="[a-zA-Z_]\w*"><token type="Name"/></rule>
    </state>
    <state name="root">
      <rule><include state="whitespace"/></rule>
      <rule><push state="statement"/></rule>
    </state>
    <state name="statement">
      <rule><include state="whitespace"/></rule>
      <rule><include state="statements"/></rule>
      <rule pattern="[{}]"><token type="Punctuation"/></rule>
      <rule pattern=";"><token type="Punctuation"/><pop depth="1"/></rule>
    </state>
    <state name="string">
      <rule pattern="&quot;"><token type="LiteralString"/><pop depth="1"/></rule>
      <rule pattern="\\([\\abfnrtv&quot;\&#x27;]|x[a-fA-F0-9]{2,4}|[0-7]{1,3})"><token type="LiteralStringEscape"/></rule>
      <rule pattern="[^\\&quot;\n]+"><token type="LiteralString"/></rule>
      <rule pattern="\\\n"><token type="LiteralString"/></rule>
      <rule pattern="\\"><token type="LiteralString"/></rule>
    </state>
    <state name="if0">
      <rule pattern="^\s*#if.*?(?&lt;!\\)\n"><token type="CommentPreproc"/><push/></rule>
      <rule pattern="^\s*#el(?:se|if).*\n"><token type="CommentPreproc"/><pop depth="1"/></rule>
      <rule pattern="^\s*#endif.*?(?&lt;!\\)\n"><token type="CommentPreproc"/><pop depth="1"/></rule>
      <rule pattern=".*?\n"><token type="Comment"/></rule>
    </state>
    <state name="class">
      <rule pattern="[a-zA-Z_]\w*"><token type="NameClass"/><pop depth="1"/></rule>
    </state>
    <state name="namespace">
      <rule pattern="[a-zA-Z_][\w.]*"><token type="NameNamespace"/><pop depth="1"/></rule>
    </state>
  </rules>
</lexer>
