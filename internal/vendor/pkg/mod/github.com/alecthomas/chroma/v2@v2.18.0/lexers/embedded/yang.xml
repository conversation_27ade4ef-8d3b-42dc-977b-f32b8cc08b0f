<lexer>
  <config>
    <name>YANG</name>
    <alias>yang</alias>
    <filename>*.yang</filename>
    <mime_type>application/yang</mime_type>
  </config>
  <rules>
    <state name="root">
      <rule pattern="\s+">
        <token type="TextWhitespace"/>
      </rule>
      <rule pattern="[\{\}\;]+">
        <token type="Punctuation"/>
      </rule>
      <rule pattern="(?&lt;![\-\w])(and|or|not|\+|\.)(?![\-\w])">
        <token type="Operator"/>
      </rule>
      <rule pattern="&#34;(?:\\&#34;|[^&#34;])*?&#34;">
        <token type="LiteralStringDouble"/>
      </rule>
      <rule pattern="&#39;(?:\\&#39;|[^&#39;])*?&#39;">
        <token type="LiteralStringSingle"/>
      </rule>
      <rule pattern="/\*">
        <token type="CommentMultiline"/>
        <push state="comments"/>
      </rule>
      <rule pattern="//.*?$">
        <token type="CommentSingle"/>
      </rule>
      <rule pattern="(?:^|(?&lt;=[\s{};]))([\w.-]+)(:)([\w.-]+)(?=[\s{};])">
        <bygroups>
          <token type="KeywordNamespace"/>
          <token type="Punctuation"/>
          <token type="Text"/>
        </bygroups>
      </rule>
      <rule pattern="([0-9]{4}\-[0-9]{2}\-[0-9]{2})(?=[\s\{\}\;])">
        <token type="LiteralDate"/>
      </rule>
      <rule pattern="([0-9]+\.[0-9]+)(?=[\s\{\}\;])">
        <token type="LiteralNumberFloat"/>
      </rule>
      <rule pattern="([0-9]+)(?=[\s\{\}\;])">
        <token type="LiteralNumberInteger"/>
      </rule>
      <rule pattern="(submodule|module)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(yang-version|belongs-to|namespace|prefix)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(organization|description|reference|revision|contact)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(revision-date|include|import)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(notification|if-feature|deviation|extension|identity|argument|grouping|typedef|feature|augment|output|action|input|rpc)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(leaf-list|container|presence|anydata|deviate|choice|config|anyxml|refine|leaf|must|list|case|uses|when)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(require-instance|fraction-digits|error-app-tag|error-message|min-elements|max-elements|yin-element|ordered-by|position|modifier|default|pattern|length|status|units|value|range|type|path|enum|base|bit)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(mandatory|unique|key)(?=[^\w\-\:])">
        <token type="Keyword"/>
      </rule>
      <rule pattern="(not-supported|invert-match|deprecated|unbounded|obsolete|current|replace|delete|false|true|user|min|max|add)(?=[^\w\-\:])">
        <token type="NameClass"/>
      </rule>
      <rule pattern="(instance-identifier|identityref|enumeration|decimal64|boolean|leafref|uint64|uint32|string|binary|uint16|int32|int64|int16|empty|uint8|union|int8|bits)(?=[^\w\-\:])">
        <token type="NameClass"/>
      </rule>
      <rule pattern="[^;{}\s\&#39;\&#34;]+">
        <token type="Text"/>
      </rule>
    </state>
    <state name="comments">
      <rule pattern="[^*/]">
        <token type="CommentMultiline"/>
      </rule>
      <rule pattern="/\*">
        <token type="CommentMultiline"/>
        <push state="comment"/>
      </rule>
      <rule pattern="\*/">
        <token type="CommentMultiline"/>
        <pop depth="1"/>
      </rule>
      <rule pattern="[*/]">
        <token type="CommentMultiline"/>
      </rule>
    </state>
  </rules>
</lexer>