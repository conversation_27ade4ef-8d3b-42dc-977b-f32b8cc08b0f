[{"type": "NameFunction", "value": "RAND"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "rand"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "Collections"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "COUNT_DISTINCT"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "COUNT"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "not_null"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "REMOVE_VALUE"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "group::func"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "NameFunction", "value": "GROUP_57::F9_"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n\n"}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Operator", "value": "::"}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "LiteralNumberInteger", "value": "1"}, {"type": "NameFunction", "value": "SUM"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "Name", "value": "_G"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "A"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": "\n"}, {"type": "Name", "value": "_aql"}, {"type": "Operator", "value": "::"}, {"type": "NameFunction", "value": "avg"}, {"type": "Punctuation", "value": "()"}]