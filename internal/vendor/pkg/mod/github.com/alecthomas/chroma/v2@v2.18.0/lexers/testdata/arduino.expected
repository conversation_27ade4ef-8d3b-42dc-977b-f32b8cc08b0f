[{"type": "CommentPreproc", "value": "#include"}, {"type": "Text", "value": " "}, {"type": "CommentPreprocFile", "value": "<Servo.h>"}, {"type": "CommentPreproc", "value": "\n#include"}, {"type": "Text", "value": " "}, {"type": "CommentPreprocFile", "value": "<Adafruit_NeoPixel.h>"}, {"type": "CommentPreproc", "value": "\n"}, {"type": "Text", "value": "\n"}, {"type": "NameClass", "value": "Servo"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "elbow_servo"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": " \n"}, {"type": "NameClass", "value": "Servo"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "wrist_servo"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "Keyword", "value": "const"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "potpin"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "// analog pin used to connect the potentiometer\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "val"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "    "}, {"type": "CommentSingle", "value": "// variable to read the value from the analog pin\n"}, {"type": "Text", "value": "\n"}, {"type": "CommentSingle", "value": "//// button cycles through controlling each color\n//// in the neopixel.  state 0=stop, 1=red, 2=blue, 3=green, 4=all/white\n"}, {"type": "Keyword", "value": "const"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "button_pin"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "4"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "buttonState"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "             "}, {"type": "CommentSingle", "value": "// the current reading from the input pin\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "lastButtonState"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "KeywordConstant", "value": "HIGH"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "   "}, {"type": "CommentSingle", "value": "// the previous reading from the input pin\n// the following variables are unsigned longs because the time, measured in\n// milliseconds, will quickly become a bigger number than can be stored in an int.\n"}, {"type": "KeywordType", "value": "unsigned"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "long"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "lastDebounceTime"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "// the last time the output pin was toggled\n"}, {"type": "KeywordType", "value": "unsigned"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "long"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "deboun<PERSON><PERSON><PERSON><PERSON>"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "50"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "    "}, {"type": "CommentSingle", "value": "// the debounce time; increase if the output flickers\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "led_control_state"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "const"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "pixel_pin"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "6"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "Keyword", "value": "const"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "num_leds"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "2"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "Name", "value": "Adafruit_NeoPixel"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "pixel"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Name", "value": "Adafruit_NeoPixel"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "num_leds"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "pixel_pin"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "NEO_GRB"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "NEO_KHZ800"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "red"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "green"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "blue"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "setup"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n  "}, {"type": "NameClass", "value": "Serial"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "begin"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralNumberInteger", "value": "9600"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "\n  "}, {"type": "Name", "value": "elbow_servo"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "attach"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralNumberInteger", "value": "9"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "// attaches the servo on pin 9 to the servo object\n"}, {"type": "Text", "value": "  "}, {"type": "NameFunction", "value": "pinMode"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "button_pin"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "KeywordConstant", "value": "INPUT_PULLUP"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": " "}, {"type": "CommentSingle", "value": "// create a button for the neopixels\n"}, {"type": "Text", "value": "  "}, {"type": "Name", "value": "pixel"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "begin"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "loop"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n  \n  "}, {"type": "CommentSingle", "value": "//// Read signal from variable resistor or potentiometer\n"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "//\n"}, {"type": "Text", "value": "  "}, {"type": "Name", "value": "val"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "analogRead"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "potpin"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "            "}, {"type": "CommentSingle", "value": "// reads the value of the potentiometer (value between 0 and 1023)\n"}, {"type": "Text", "value": "  "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "v"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "map"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "val"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "1023"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "180"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "     "}, {"type": "CommentSingle", "value": "// scale it to use it with the servo (value between 0 and 180)\n"}, {"type": "Text", "value": "  "}, {"type": "Name", "value": "elbow_servo"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "write"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "v"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "  \n  "}, {"type": "NameFunction", "value": "delay"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralNumberInteger", "value": "15"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "                           "}, {"type": "CommentSingle", "value": "// waits for the servo to get there\n"}, {"type": "Text", "value": "\n\n  "}, {"type": "CommentSingle", "value": "//// Toggle between states using a button\n"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "////\n"}, {"type": "Text", "value": "  "}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "digitalRead"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "button_pin"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": " "}, {"type": "CommentSingle", "value": "// read button\n"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "// \"debounce\" the button signal\n"}, {"type": "Text", "value": "  "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "!="}, {"type": "Text", "value": " "}, {"type": "Name", "value": "lastButtonState"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "lastDebounceTime"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "millis"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n  "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "(("}, {"type": "NameFunction", "value": "millis"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "-"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "lastDebounceTime"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": ">"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "deboun<PERSON><PERSON><PERSON><PERSON>"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n    "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "!="}, {"type": "Text", "value": " "}, {"type": "Name", "value": "buttonState"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "buttonState"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "buttonState"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "=="}, {"type": "Text", "value": " "}, {"type": "KeywordConstant", "value": "LOW"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n        "}, {"type": "Name", "value": "led_control_state"}, {"type": "Operator", "value": "++"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n        "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "led_control_state"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": ">"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "3"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "led_control_state"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n  "}, {"type": "Name", "value": "lastButtonState"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n  "}, {"type": "Keyword", "value": "switch"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "led_control_state"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n    "}, {"type": "Keyword", "value": "case"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": "\n      "}, {"type": "Keyword", "value": "break"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": " \n    "}, {"type": "Keyword", "value": "case"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "1"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "red"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "blue"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "green"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Keyword", "value": "break"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n    "}, {"type": "Keyword", "value": "case"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "2"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "red"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "blue"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "green"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Keyword", "value": "break"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n    "}, {"type": "Keyword", "value": "case"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "3"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "red"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "blue"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Name", "value": "green"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "255"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n      "}, {"type": "Keyword", "value": "break"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n  "}, {"type": "NameClass", "value": "Serial"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "println"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "led_control_state"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "\n  "}, {"type": "CommentSingle", "value": "// set color ofpixels\n"}, {"type": "Text", "value": "  "}, {"type": "Keyword", "value": "for"}, {"type": "Punctuation", "value": "("}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "i"}, {"type": "Operator", "value": "="}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ";"}, {"type": "Name", "value": "i"}, {"type": "Operator", "value": "<"}, {"type": "Name", "value": "num_leds"}, {"type": "Punctuation", "value": ";"}, {"type": "Name", "value": "i"}, {"type": "Operator", "value": "++"}, {"type": "Punctuation", "value": "){"}, {"type": "Text", "value": "\n    "}, {"type": "Name", "value": "pixel"}, {"type": "Punctuation", "value": "."}, {"type": "Name", "value": "setPixelColor"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "i"}, {"type": "Punctuation", "value": ","}, {"type": "Name", "value": "red"}, {"type": "Punctuation", "value": ","}, {"type": "Name", "value": "green"}, {"type": "Punctuation", "value": ","}, {"type": "Name", "value": "blue"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "\n    "}, {"type": "Name", "value": "pixel"}, {"type": "Punctuation", "value": "."}, {"type": "Name", "value": "show"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": " \n    "}, {"type": "NameFunction", "value": "delay"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralNumberInteger", "value": "15"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": " \n  "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "}"}]