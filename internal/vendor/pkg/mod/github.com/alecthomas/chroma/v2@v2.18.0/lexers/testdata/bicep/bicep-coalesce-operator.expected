[{"type": "KeywordDeclaration", "value": "output"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "nonNullStr"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "string"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "myObject"}, {"type": "Punctuation", "value": "."}, {"type": "NameVariable", "value": "isnull1"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "??"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "myObject"}, {"type": "Punctuation", "value": "."}, {"type": "NameVariable", "value": "string"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "??"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "myObject"}, {"type": "Punctuation", "value": "."}, {"type": "NameVariable", "value": "isnull2"}]