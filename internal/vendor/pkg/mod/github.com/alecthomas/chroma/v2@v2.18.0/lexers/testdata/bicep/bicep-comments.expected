[{"type": "CommentSingle", "value": "// This is your primary NIC."}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordDeclaration", "value": "resource"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "nic1"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'Microsoft.Network/networkInterfaces@2020-06-01'"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Operator", "value": "/*"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "NameVariable", "value": "This"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "Bicep"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "file"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "assumes"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "the"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "key"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "vault"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "already"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "exists"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "and"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "NameVariable", "value": "is"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordDeclaration", "value": "in"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "same"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "subscription"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "and"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordDeclaration", "value": "resource"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "group"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordDeclaration", "value": "as"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "the"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "deployment"}, {"type": "Punctuation", "value": "."}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Operator", "value": "*/"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordDeclaration", "value": "param"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "existingKeyVaultName"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "string"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "KeywordDeclaration", "value": "resource"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "test"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'Microsoft.AAD/domainServices@2021-03-01'"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "NameVariable", "value": "name"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'asdfsdf'"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "CommentSingle", "value": "// this is a comment"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "NameVariable", "value": "properties"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "CommentMultiline", "value": "/*comment*/"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "domainConfigurationType"}, {"type": "CommentMultiline", "value": "/*comment*/"}, {"type": "Punctuation", "value": ":"}, {"type": "CommentMultiline", "value": "/*comment*/"}, {"type": "LiteralString", "value": "'as//notacomment!d/* also not a comment */fsdf'"}, {"type": "CommentSingle", "value": "// test!/*"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Operator", "value": "/*"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "multi"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "line"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "comment"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "*/"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "domainName"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "/*"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "asdf"}, {"type": "Operator", "value": "*/"}, {"type": "LiteralString", "value": "'test'"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "CommentSingle", "value": "// comment"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "Punctuation", "value": "}"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "}"}]