[{"type": "Punctuation", "value": "@"}, {"type": "NameFunction", "value": "description"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralString", "value": "'The name of the instance.'"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordDeclaration", "value": "param"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "name"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "string"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "@"}, {"type": "NameVariable", "value": "sys"}, {"type": "Punctuation", "value": "."}, {"type": "NameFunction", "value": "description"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralString", "value": "'The description of the instance to display.'"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordDeclaration", "value": "param"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "description"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "string"}]