[{"type": "KeywordDeclaration", "value": "param"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "exampleString"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "string"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'test value'"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "KeywordDeclaration", "value": "var"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "comments"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'''\ncomments // are included\n/* because everything is read as-is */\n'''"}]