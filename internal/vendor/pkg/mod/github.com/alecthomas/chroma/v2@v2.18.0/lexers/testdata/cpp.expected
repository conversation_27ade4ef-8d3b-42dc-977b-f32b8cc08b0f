[{"type": "CommentPreproc", "value": "#include"}, {"type": "Text", "value": " "}, {"type": "CommentPreprocFile", "value": "\"a\""}, {"type": "CommentPreproc", "value": " "}, {"type": "CommentSingle", "value": "// comment\n"}, {"type": "CommentPreproc", "value": "#include"}, {"type": "Text", "value": " "}, {"type": "CommentPreprocFile", "value": "<b>"}, {"type": "CommentPreproc", "value": " "}, {"type": "CommentSingle", "value": "// comment\n"}, {"type": "Text", "value": "\n"}, {"type": "NameAttribute", "value": "[[nodiscard]]"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "foo"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "noexcept"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "foo"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "constexpr"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "class"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{}"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "enum"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "class"}, {"type": "Text", "value": " "}, {"type": "NameClass", "value": "E"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "A"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "B"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "};"}, {"type": "Text", "value": "\n"}, {"type": "Keyword", "value": "enum"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "class"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "[[nodiscard]]"}, {"type": "Text", "value": " "}, {"type": "NameClass", "value": "E"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "A"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "B"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "};"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "enum"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "class"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n  "}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": "\n  "}, {"type": "Name", "value": "c"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "e"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "enum"}, {"type": "Text", "value": " "}, {"type": "NameClass", "value": "E"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "A"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "B"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "};"}, {"type": "Text", "value": "\n\n"}, {"type": "Keyword", "value": "class"}, {"type": "Text", "value": " "}, {"type": "NameClass", "value": "A"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n  "}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "foo"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n  "}, {"type": "KeywordType", "value": "void"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "bar"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "};"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordType", "value": "int"}, {"type": "Text", "value": " "}, {"type": "NameFunction", "value": "main"}, {"type": "Punctuation", "value": "()"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n    "}, {"type": "Keyword", "value": "return"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "1'3"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberFloat", "value": "1.4"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}]