[{"type": "CommentSingle", "value": "// test comment\n"}, {"type": "KeywordNamespace", "value": "use"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "bufio"}, {"type": "Operator", "value": "::*"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordNamespace", "value": "use"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "fmt"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "os"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "exec"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "exec"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordNamespace", "value": "use"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "time"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "type"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "foo"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "struct"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "bar"}, {"type": "Operator", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordType", "value": "str"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "baz"}, {"type": "Operator", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "time"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "duration"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "};"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "export"}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "type"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "e"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "enum"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordType", "value": "u8"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "c"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "};"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "const"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "arr"}, {"type": "Operator", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "["}, {"type": "Keyword", "value": "_"}, {"type": "Punctuation", "value": "]"}, {"type": "Name", "value": "foo"}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "["}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "foo"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "bar"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "\"This is a "}, {"type": "LiteralStringEscape", "value": "\\\""}, {"type": "LiteralString", "value": "string"}, {"type": "LiteralStringEscape", "value": "\\\""}, {"type": "LiteralString", "value": "!\""}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "baz"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "25"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "*"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "time"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "MINUTE"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "foo"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "bar"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "`This is also a\nstring`"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "baz"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "5"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "*"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "time"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "SECOND"}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "];"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "let"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "c"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralStringChar", "value": "'a'"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": " "}, {"type": "CommentSingle", "value": "// char\n"}, {"type": "Keyword", "value": "let"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "d"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberBin", "value": "0b1010u16"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Keyword", "value": "let"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "e"}, {"type": "Operator", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordType", "value": "size"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "32z"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Keyword", "value": "let"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "g"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberHex", "value": "0xffa31u32"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberOct", "value": "0o3u32"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "export"}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "fn"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "main"}, {"type": "Punctuation", "value": "()"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordType", "value": "void"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Name", "value": "fmt"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "println"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralString", "value": "\"{}\""}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "size"}, {"type": "Punctuation", "value": "("}, {"type": "KeywordType", "value": "int"}, {"type": "Punctuation", "value": "))"}, {"type": "Operator", "value": "!"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n\n\t"}, {"type": "Keyword", "value": "for"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Keyword", "value": "let"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "i"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "0z"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "i"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "<"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "5"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "i"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "+="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "1"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n\t\t"}, {"type": "Name", "value": "fmt"}, {"type": "Operator", "value": "::"}, {"type": "Name", "value": "println"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralString", "value": "\"{}\""}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "i"}, {"type": "Punctuation", "value": ");"}, {"type": "TextWhitespace", "value": "\n\t"}, {"type": "Punctuation", "value": "};"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "};"}, {"type": "TextWhitespace", "value": "\n"}]