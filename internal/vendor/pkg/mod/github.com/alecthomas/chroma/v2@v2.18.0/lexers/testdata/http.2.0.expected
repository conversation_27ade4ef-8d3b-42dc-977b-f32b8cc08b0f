[{"type": "NameFunction", "value": "GET"}, {"type": "Text", "value": " "}, {"type": "NameNamespace", "value": "/foo"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "HTTP"}, {"type": "Operator", "value": "/"}, {"type": "LiteralNumber", "value": "2.0"}, {"type": "Text", "value": "\n"}, {"type": "Name", "value": "Content-Type"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "Literal", "value": "application/json"}, {"type": "Text", "value": "\n"}, {"type": "Name", "value": "User-Agent"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "Literal", "value": "foo"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "{"}, {"type": "NameTag", "value": "\"hello\""}, {"type": "Punctuation", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringDouble", "value": "\"world\""}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}]