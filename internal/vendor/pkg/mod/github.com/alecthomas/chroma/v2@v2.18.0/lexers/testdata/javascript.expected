[{"type": "NameOther", "value": "sum"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "parseInt"}, {"type": "Punctuation", "value": "("}, {"type": "NameOther", "value": "num1"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "parseInt"}, {"type": "Punctuation", "value": "("}, {"type": "NameOther", "value": "num2"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "CommentSingle", "value": "// \"+\" means \"add\"\n"}, {"type": "NameOther", "value": "alert"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralStringDouble", "value": "\"Sum = \""}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "sum"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": "  "}, {"type": "CommentSingle", "value": "// \"+\" means combine into a string\n"}, {"type": "KeywordReserved", "value": "const"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "filePath"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "String"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "raw"}, {"type": "LiteralStringBacktick", "value": "`C:\\Development\\profile\\aboutme.html`"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordDeclaration", "value": "let"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "n"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberHex", "value": "0x21"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "1_000"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordDeclaration", "value": "let"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "n2"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralNumberFloat", "value": "1e3"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}]