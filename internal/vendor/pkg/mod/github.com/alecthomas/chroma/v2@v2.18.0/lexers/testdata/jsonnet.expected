[{"type": "Keyword", "value": "local"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "application"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'my-app'"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Keyword", "value": "local"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "module"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'uwsgi_module'"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Keyword", "value": "local"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "dir"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'/var/www'"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Keyword", "value": "local"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "permission"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberFloat", "value": "644"}, {"type": "Punctuation", "value": ";"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "NameVariable", "value": "'uwsgi.ini'"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "std.manifestIni"}, {"type": "Punctuation", "value": "({"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "sections"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n      "}, {"type": "NameVariable", "value": "uwsgi"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "module"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "module"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "pythonpath"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "dir"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "socket"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "dir"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "+"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'/uwsgi.sock'"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "'chmod-socket'"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "permission"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "callable"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "application"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "logto"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'/var/log/uwsgi/uwsgi.log'"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n      "}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "Punctuation", "value": "}),"}, {"type": "TextWhitespace", "value": "\n\n  "}, {"type": "NameVariable", "value": "'init.sh'"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "|||\n    #!/usr/bin/env bash\n    mkdir -p %(dir)s\n    touch %(dir)s/initialized\n    chmod %(perm)d %(dir)s/initialized\n  |||"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "%"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "NameVariable", "value": "dir"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "dir"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "perm"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "permission"}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n\n  "}, {"type": "NameVariable", "value": "'cassandra.conf'"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "std.manifestYamlDoc"}, {"type": "Punctuation", "value": "({"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "cluster_name"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "application"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "NameVariable", "value": "seed_provider"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "["}, {"type": "TextWhitespace", "value": "\n      "}, {"type": "Punctuation", "value": "{"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "class_name"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'SimpleSeedProvider'"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "NameVariable", "value": "parameters"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "[{"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "seeds"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralString", "value": "'127.0.0.1'"}, {"type": "TextWhitespace", "value": " "}, {"type": "Punctuation", "value": "}],"}, {"type": "TextWhitespace", "value": "\n      "}, {"type": "Punctuation", "value": "},"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Punctuation", "value": "],"}, {"type": "TextWhitespace", "value": "\n  "}, {"type": "Punctuation", "value": "}),"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "TextWhitespace", "value": "\n"}]