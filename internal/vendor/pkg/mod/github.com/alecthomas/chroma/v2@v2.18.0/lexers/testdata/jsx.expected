[{"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "React"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'react'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "ReactDOM"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'react-dom'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "App"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'./component/App'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'./index.css'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'github-fork-ribbon-css/gh-fork-ribbon.css'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n"}, {"type": "NameOther", "value": "ReactDOM"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "render"}, {"type": "Punctuation", "value": "("}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "<>"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "React.StrictMode"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n      "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "App"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "/>"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "React.StrictMode"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "</>,"}, {"type": "Text", "value": "\n  "}, {"type": "NameBuiltin", "value": "document"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "getElementById"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralStringSingle", "value": "'root'"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": ");"}, {"type": "Text", "value": "\n"}]