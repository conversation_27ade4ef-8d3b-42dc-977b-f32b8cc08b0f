package X

import A
import B.*

fun main(args: Array<String>) {
    val ` + "with spaces" + ` = "hello"
    val multiline = """1
2"""
    StringBuilder().apply {
        append(multiline)
    }
    val unsigned = 0x00UL + 123u + 76.54
}
/*
*/

fun nullable(nullable: String?): Int = nullable?.length ?: 0

fun nullable2(nullable: String?): Int = nullable?.length ?: run {
    1 + 2
}

val rawStringWithQuotes = """
Hello "example" ${1 + 2}
And now { Just the braces }
Escapes here don't work so this is just text \t \n \u1234 $ \$
"""

fun returnsSomething(): Int {
    return "".length
}

fun returnsSomethingElse(): Int = "\\\n".length

val character = '"'
val escapedCharacter = '\"'
// escaping a double quote character inside a character is optional
val stringWithSingleQuote = "'"

typealias MySecretAlias<A, B> = (A, B) -> Unit

val impl : MySecretAlias<Int, Int> = { a, _ -> Unit }

fun someOperators(a: Int, b: Int) {
    var c : Int = 123
    println("This is an example a = $a and the sum is ${a + b} ${ A.foo() }")
    println( a % b)
    println(c in a..b)
    
    Logger.info("Hello World")
    
    a %= 2
	a && b
	a *= 2
	a++
	b+=2
	b--
	a-=1
	a /= 2
	a <= b
	b == a
	a >= b
	nullable!!
	a != b
	a || b
	a?.foo
	a ?: b
}

class B // no braces

data class C(
    val s: String
)

fun moreOperators(arg: Any?) {
    when(arg) {
        !is Int -> { }
        is Number -> {}
        in emptyList<Int>() -> { }
    }

    if(arg !== Boolean)
    	println(arg === Unit)
}

@Target(AnnotationTarget.CLASS, AnnotationTarget.FUNCTION,
        AnnotationTarget.VALUE_PARAMETER, AnnotationTarget.EXPRESSION)
@Retention(AnnotationRetention.SOURCE)
annotation class Annotated

@Annotated class A {
    @Annotated fun a(
        @Annotated param: Int
    ) {

        @Annotated val y = 0

    }
}

open class TUpper
fun <T: TUpper, R: Any?> generic() = 123

class Holder <in A, out B, C: TUpper>

class Holder2  <T>

var holder1: Holder<Int, String, *>? = null

class Some(
    val holder2: Holder<Int, String, *>? = null
) {
    var holder3: Holder<Int, String, *>? = null
    fun example() {
        var holder4: Holder<Int, String, *>? = null
    }
}
fun <T : Comparable<T>> sort(list: List<T>) {
    
}

class X {
    companion object {
    }
    suspend inline fun <reified T>  generic(t: T) { print(T::class.simpleName) }
} // eof comment