[{"type": "KeywordNamespace", "value": "from"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameNamespace", "value": "a"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordNamespace", "value": "import"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "B"}, {"type": "TextWhitespace", "value": "\n"}, {"type": "KeywordNamespace", "value": "from"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameNamespace", "value": "a"}, {"type": "TextWhitespace", "value": " "}, {"type": "KeywordNamespace", "value": "import"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "C"}, {"type": "TextWhitespace", "value": " "}, {"type": "Keyword", "value": "as"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "D"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "alias"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "ModuleInt"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "fn"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameFunction", "value": "my_func"}, {"type": "Punctuation", "value": "["}, {"type": "Name", "value": "T"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": "]("}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "T"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "->"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "T"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "var"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "s"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "Struct"}, {"type": "Punctuation", "value": "["}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": "]()"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "var"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "b"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "s.test"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "a"}, {"type": "Operator", "value": "="}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": "\n\n    "}, {"type": "Keyword", "value": "try"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "raising_func"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "except"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Keyword", "value": "return"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "finally"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Keyword", "value": "pass"}, {"type": "TextWhitespace", "value": "\n\n    "}, {"type": "Keyword", "value": "return"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "fn"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameFunction", "value": "raising_func"}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "raises"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "->"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "if"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "=="}, {"type": "TextWhitespace", "value": " "}, {"type": "LiteralNumberInteger", "value": "0"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Keyword", "value": "raise"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "ValueError"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralStringDouble", "value": "\"error message\""}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "return"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": "\n\n"}, {"type": "Keyword", "value": "struct"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameClass", "value": "Struct"}, {"type": "Punctuation", "value": "["}, {"type": "Name", "value": "T"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": "]:"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "LiteralStringDouble", "value": "\"\"\"\n    Doc<PERSON><PERSON>.\n    \n    With multiple lines.\n    \"\"\""}, {"type": "TextWhitespace", "value": "\n    \n    "}, {"type": "Keyword", "value": "alias"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "MyInt"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "="}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "LiteralStringDouble", "value": "\"\"\"Simple Docstring.\"\"\""}, {"type": "TextWhitespace", "value": "\n\n    "}, {"type": "Keyword", "value": "var"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameVariable", "value": "x"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "TextWhitespace", "value": "\n\n    "}, {"type": "Keyword", "value": "fn"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameFunction", "value": "test"}, {"type": "Punctuation", "value": "("}, {"type": "NameBuiltin<PERSON><PERSON>do", "value": "self"}, {"type": "Punctuation", "value": ","}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": ")"}, {"type": "TextWhitespace", "value": " "}, {"type": "Operator", "value": "->"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameBuiltin", "value": "Int"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Keyword", "value": "return"}, {"type": "TextWhitespace", "value": " "}, {"type": "Name", "value": "a"}, {"type": "TextWhitespace", "value": "\n\n\n"}, {"type": "Keyword", "value": "trait"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameClass", "value": "<PERSON><PERSON><PERSON>"}, {"type": "Punctuation", "value": ":"}, {"type": "TextWhitespace", "value": "\n    "}, {"type": "Keyword", "value": "fn"}, {"type": "TextWhitespace", "value": " "}, {"type": "NameFunction", "value": "dummy"}, {"type": "Punctuation", "value": "("}, {"type": "NameBuiltin<PERSON><PERSON>do", "value": "self"}, {"type": "Punctuation", "value": "):"}, {"type": "TextWhitespace", "value": "\n        "}, {"type": "Operator", "value": "..."}, {"type": "TextWhitespace", "value": "\n"}]