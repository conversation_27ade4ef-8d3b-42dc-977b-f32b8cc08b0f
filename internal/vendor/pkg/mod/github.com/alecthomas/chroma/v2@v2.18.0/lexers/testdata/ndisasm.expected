[{"type": "CommentSpecial", "value": "00000000"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "B013"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "mov"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "al"}, {"type": "Punctuation", "value": ","}, {"type": "LiteralNumberHex", "value": "0x13"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000002"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "CD10"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "int"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberHex", "value": "0x10"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000004"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "B9027D"}, {"type": "Text", "value": "            "}, {"type": "NameFunction", "value": "mov"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "cx"}, {"type": "Punctuation", "value": ","}, {"type": "LiteralNumberHex", "value": "0x7d02"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000007"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "51"}, {"type": "Text", "value": "                "}, {"type": "NameFunction", "value": "push"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "cx"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000008"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "6800A0"}, {"type": "Text", "value": "            "}, {"type": "NameFunction", "value": "push"}, {"type": "Text", "value": " "}, {"type": "KeywordType", "value": "word"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberHex", "value": "0xa000"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "0000000B"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "07"}, {"type": "Text", "value": "                "}, {"type": "NameFunction", "value": "pop"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "es"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "0000000C"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "B00F"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "mov"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "al"}, {"type": "Punctuation", "value": ","}, {"type": "LiteralNumberHex", "value": "0xf"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "0000000E"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "F3AA"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "rep"}, {"type": "Text", "value": " "}, {"type": "NameVariable", "value": "stosb"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000010"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "59"}, {"type": "Text", "value": "                "}, {"type": "NameFunction", "value": "pop"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "cx"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000011"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "B004"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "mov"}, {"type": "Text", "value": " "}, {"type": "NameBuiltin", "value": "al"}, {"type": "Punctuation", "value": ","}, {"type": "LiteralNumberHex", "value": "0x4"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000013"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "F3AA"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "rep"}, {"type": "Text", "value": " "}, {"type": "NameVariable", "value": "stosb"}, {"type": "Text", "value": "\n"}, {"type": "CommentSpecial", "value": "00000015"}, {"type": "Text", "value": "  "}, {"type": "CommentSpecial", "value": "CD16"}, {"type": "Text", "value": "              "}, {"type": "NameFunction", "value": "int"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberHex", "value": "0x16"}, {"type": "Text", "value": "\n"}]