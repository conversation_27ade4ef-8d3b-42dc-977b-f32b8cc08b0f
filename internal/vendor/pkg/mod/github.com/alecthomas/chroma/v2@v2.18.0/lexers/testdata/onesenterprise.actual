#Область СлужебныйПрограммныйИнтерфейс

&НаСервере
Функция Создать(ИмяФайла) Экспорт
	
	//Создает новый архив

	Инфо = Новый Файл(ИмяФайла);
	Если Инфо.Существует() Тогда
		УдалитьФайлы(ИмяФайла);
	КонецЕсли;
	
	Архив = Новый Структура;
	Архив.Вставить("Поток", ФайловыеПотоки.ОткрытьДляЗаписи(ИмяФайла));
	Архив.Вставить("Файлы", Новый Массив);
	Архив.Вставить("РазмерКаталога", 0);

	Если Архив.РазмерКаталога > 0 Или Архив.Файлы.Количество() <> 0 Тогда
		Возврат Неопределено;
	КонецЕсли;
	
	Возврат Архив;
	
КонецФункции

#КонецОбласти