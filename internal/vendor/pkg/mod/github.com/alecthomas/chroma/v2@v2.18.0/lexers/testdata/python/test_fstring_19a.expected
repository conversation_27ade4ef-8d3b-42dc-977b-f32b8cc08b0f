[{"type": "LiteralStringAffix", "value": "f"}, {"type": "LiteralStringSingle", "value": "'mapping is "}, {"type": "LiteralStringInterpol", "value": "{"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ":"}, {"type": "Name", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "for"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Name", "value": "a"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "b"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": " "}, {"type": "OperatorWord", "value": "in"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "(("}, {"type": "LiteralNumberInteger", "value": "1"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "2"}, {"type": "Punctuation", "value": "),"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "LiteralNumberInteger", "value": "3"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "4"}, {"type": "Punctuation", "value": "))}"}, {"type": "Text", "value": " "}, {"type": "LiteralStringInterpol", "value": "}"}, {"type": "LiteralStringSingle", "value": "'"}, {"type": "Text", "value": "\n"}]