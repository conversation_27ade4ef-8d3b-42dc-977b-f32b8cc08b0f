.class LExample;
.super Ljava/lang/Object;
.source "Example.java"


# instance fields
.field protected count:I

.field private label:Ljava/lang/String;


# direct methods
.method constructor <init>()V
    .registers 1

    .line 1
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static main([Ljava/lang/String;)V
    .registers 2

    .line 16
    sget-object p0, Ljava/lang/System;->out:Ljava/io/PrintStream;

    const-string v0, "Hello world!"

    invoke-virtual {p0, v0}, Ljava/io/PrintStream;->println(Ljava/lang/String;)V

    .line 17
    return-void
.end method


# virtual methods
.method public getLabel()Ljava/lang/String;
    .registers 2

    .line 8
    iget-object v0, p0, LExample;->label:Ljava/lang/String;

    return-object v0
.end method

.method public setLabel(Ljava/lang/String;)V
    .registers 2

    .line 12
    iput-object p1, p0, <PERSON><PERSON><PERSON><PERSON>;->label:<PERSON><PERSON><PERSON>/lang/String;

    .line 13
    return-void
.end method
