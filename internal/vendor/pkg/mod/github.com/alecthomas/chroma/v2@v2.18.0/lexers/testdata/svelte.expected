[{"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "script"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "lang"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"typescript\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t"}, {"type": "CommentSingle", "value": "// Nested is defined elsewhere\n"}, {"type": "Text", "value": "\t"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "Nested"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'./Nested.svelte'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n\t"}, {"type": "KeywordDeclaration", "value": "let"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "src"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'example.png'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\t"}, {"type": "KeywordDeclaration", "value": "let"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "name"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'world'"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\n\t"}, {"type": "KeywordDeclaration", "value": "let"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "names"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "["}, {"type": "Text", "value": "\n\t\t"}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "id"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'id0001'"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "name"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'Name 1'"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "},"}, {"type": "Text", "value": "\n\t\t"}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "id"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'id0002'"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "name"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'Name 2'"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "},"}, {"type": "Text", "value": "\n\t\t"}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "id"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'id0003'"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "name"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'Name 3'"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "];"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "script"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "style"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t"}, {"type": "NameTag", "value": "h1"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n\t\t"}, {"type": "Keyword", "value": "color"}, {"type": "Punctuation", "value": ":"}, {"type": "Text", "value": " "}, {"type": "KeywordConstant", "value": "red"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\t\t"}, {"type": "Keyword", "value": "font-family"}, {"type": "Punctuation", "value": ":"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "<PERSON><PERSON>"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "Helvetica"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "KeywordConstant", "value": "sans-serif"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\t\t"}, {"type": "Keyword", "value": "font-size"}, {"type": "Punctuation", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "2"}, {"type": "KeywordType", "value": "em"}, {"type": "Punctuation", "value": ";"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "style"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "style"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "lang"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"sass\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n"}, {"type": "NameVariable", "value": "$color"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "NameEntity", "value": "red"}, {"type": "Text", "value": "\n"}, {"type": "NameTag", "value": "h1"}, {"type": "Text", "value": "\n"}, {"type": "Error", "value": "  "}, {"type": "NameAttribute", "value": "color"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "NameVariable", "value": "$color"}, {"type": "Text", "value": "\n"}, {"type": "Error", "value": "  "}, {"type": "NameAttribute", "value": "font-family"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "Name", "value": "<PERSON><PERSON>"}, {"type": "Operator", "value": ","}, {"type": "Text", "value": " "}, {"type": "Name", "value": "Helvetica"}, {"type": "Operator", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameConstant", "value": "sans-serif"}, {"type": "Text", "value": "\n"}, {"type": "Error", "value": "  "}, {"type": "NameAttribute", "value": "font-size"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "2"}, {"type": "KeywordType", "value": "em"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "style"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "h1"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "Hello "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "name"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "!"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "h1"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "img"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "src"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "alt"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"Example image\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "Comment", "value": "<!-- import external component -->"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "Nested"}, {"type": "Punctuation", "value": "/>"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "ul"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#each"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "names"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "as"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "id"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "name"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "},"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "i"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "li"}, {"type": "Punctuation", "value": ">{"}, {"type": "NameOther", "value": "name"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": " ("}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "id"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": ")"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "li"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/each"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "ul"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "template"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "form"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "on:submit"}, {"type": "Operator", "value": "|"}, {"type": "NameAttribute", "value": "preventDefault"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\""}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "submitSearch"}, {"type": "Punctuation", "value": "}"}, {"type": "LiteralString", "value": "\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n        "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "input"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "type"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"search\""}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "bind:value"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\""}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "name"}, {"type": "Punctuation", "value": "}"}, {"type": "LiteralString", "value": "\""}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "required"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "/>"}, {"type": "Text", "value": "\n        "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "button"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "type"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"submit\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "Search"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "button"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "form"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#if"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "porridge"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "temperature"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": ">"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "100"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "too hot!"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": ":else"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "if"}, {"type": "Text", "value": " "}, {"type": "LiteralNumberInteger", "value": "80"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": ">"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "porridge"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "temperature"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "too cold!"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Operator", "value": ":"}, {"type": "Keyword", "value": "else"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "just right!"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/if"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#await"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "promise"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Comment", "value": "<!-- promise is pending -->"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "waiting for the promise to resolve..."}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": ":then"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Comment", "value": "<!-- promise was fulfilled -->"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "The value is "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": ":catch"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "error"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Comment", "value": "<!-- promise was rejected -->"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "Something went wrong: "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "error"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "message"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/await"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#await"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "promise"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "then"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "The value is "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/await"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#await"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "promise"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "catch"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "error"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "The error is "}, {"type": "Punctuation", "value": "{"}, {"type": "NameOther", "value": "error"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/await"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "#key"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\t"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "div"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "transition:fade"}, {"type": "Punctuation", "value": ">{"}, {"type": "NameOther", "value": "value"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "div"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "/key"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "div"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "class"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"blog-post\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "h1"}, {"type": "Punctuation", "value": ">{"}, {"type": "NameOther", "value": "post"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "title"}, {"type": "Punctuation", "value": "}</"}, {"type": "NameTag", "value": "h1"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "@html"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "post"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "content"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "div"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\n    "}, {"type": "Punctuation", "value": "{"}, {"type": "Keyword", "value": "@debug"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "user"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "template"}, {"type": "Punctuation", "value": ">"}]