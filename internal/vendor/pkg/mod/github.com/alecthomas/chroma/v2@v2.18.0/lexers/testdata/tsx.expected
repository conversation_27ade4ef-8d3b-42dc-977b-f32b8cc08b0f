[{"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "React"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'react'"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "ReactDOM"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'react-dom'"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'./index.css'"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "import"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "App"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "from"}, {"type": "Text", "value": " "}, {"type": "LiteralStringSingle", "value": "'./App'"}, {"type": "Text", "value": "\n\n"}, {"type": "NameOther", "value": "ReactDOM"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "render"}, {"type": "Punctuation", "value": "("}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "<>"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "React.StrictMode"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n      "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "App"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "/>"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "React.StrictMode"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "</>,"}, {"type": "Text", "value": "\n  "}, {"type": "NameBuiltin", "value": "document"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "getElementById"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralStringSingle", "value": "'root'"}, {"type": "Punctuation", "value": "),"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": "\n\n"}, {"type": "NameOther", "value": "ReactDOM"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "render"}, {"type": "Punctuation", "value": "("}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "p"}, {"type": "Text", "value": "\n  "}, {"type": "CommentSingle", "value": "// This is a inline comment.\n"}, {"type": "Text", "value": "  "}, {"type": "CommentMultiline", "value": "/*\n    This is a\n    Multi line comment\n  */"}, {"type": "Text", "value": "\n  "}, {"type": "Comment", "value": "<!-- Another multiline\n  comment !-->"}, {"type": "Text", "value": "\n  "}, {"type": "NameAttribute", "value": "data-test-id"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"outro\""}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "disabled"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n\t"}, {"type": "NameOther", "value": "Some"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "text"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "here"}, {"type": "Punctuation", "value": "."}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "p"}, {"type": "Punctuation", "value": ">,"}, {"type": "Text", "value": "\n  "}, {"type": "NameBuiltin", "value": "document"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "getElementById"}, {"type": "Punctuation", "value": "("}, {"type": "LiteralStringSingle", "value": "'root'"}, {"type": "Punctuation", "value": "),"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": ")"}]