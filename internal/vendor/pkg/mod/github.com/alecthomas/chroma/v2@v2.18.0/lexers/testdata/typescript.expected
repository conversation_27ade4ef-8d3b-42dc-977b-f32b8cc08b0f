[{"type": "KeywordReserved", "value": "const"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "moduleName"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "modules"}, {"type": "Punctuation", "value": "."}, {"type": "NameOther", "value": "map"}, {"type": "Punctuation", "value": "();"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordReserved", "value": "declare"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "module"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "\"fs\""}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{}"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "declare"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "module"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "\"@custom/plugin\""}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{}"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "declare"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "module"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "\"../../compiler/types\""}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{}"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordReserved", "value": "type"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "TestType"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "T"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "["}, {"type": "NameOther", "value": "Key"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "keyof"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "T"}, {"type": "Punctuation", "value": "]"}, {"type": "Operator", "value": ":"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "Required"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "Pick"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "T"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "Key"}, {"type": "Punctuation", "value": ">>"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordReserved", "value": "const"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "TestComponent"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "({"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "a"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "})"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "=>"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "("}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "Component"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "width"}, {"type": "Operator", "value": "="}, {"type": "Punctuation", "value": "{"}, {"type": "LiteralNumberInteger", "value": "100"}, {"type": "Punctuation", "value": "}"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "prop"}, {"type": "Operator", "value": "="}, {"type": "Punctuation", "value": "{{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "a"}, {"type": "Punctuation", "value": ","}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "..."}, {"type": "NameOther", "value": "b"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "}}"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "attr"}, {"type": "Operator", "value": "="}, {"type": "LiteralString", "value": "\"text\""}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "span"}, {"type": "Punctuation", "value": ">"}, {"type": "NameOther", "value": "This"}, {"type": "Text", "value": " "}, {"type": "Keyword", "value": "is"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "a"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "component"}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "span"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n    "}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "SomethingElse"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "/>"}, {"type": "Text", "value": "\n  "}, {"type": "Punctuation", "value": "</"}, {"type": "NameTag", "value": "Component"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": "\n"}, {"type": "Punctuation", "value": ")"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordReserved", "value": "type"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "FirstChar"}, {"type": "Punctuation", "value": "<"}, {"type": "NameTag", "value": "K"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "extends"}, {"type": "Text", "value": " "}, {"type": "NameAttribute", "value": "string"}, {"type": "Punctuation", "value": ">"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "K"}, {"type": "Text", "value": " "}, {"type": "KeywordReserved", "value": "extends"}, {"type": "Text", "value": " "}, {"type": "LiteralStringBacktick", "value": "`"}, {"type": "LiteralStringInterpol", "value": "${"}, {"type": "Keyword", "value": "infer"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "X"}, {"type": "LiteralStringInterpol", "value": "}${"}, {"type": "Keyword", "value": "infer"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "_"}, {"type": "LiteralStringInterpol", "value": "}"}, {"type": "LiteralStringBacktick", "value": "`"}, {"type": "Text", "value": "\n  "}, {"type": "Operator", "value": "?"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "X"}, {"type": "Text", "value": "\n  : "}, {"type": "KeywordType", "value": "never"}, {"type": "Text", "value": "\n"}, {"type": "KeywordReserved", "value": "type"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "SChar"}, {"type": "Text", "value": " "}, {"type": "Operator", "value": "="}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "FirstChar"}, {"type": "Operator", "value": "<"}, {"type": "LiteralStringDouble", "value": "\"stuff\""}, {"type": "Operator", "value": ">"}, {"type": "Text", "value": "\n\n"}, {"type": "KeywordReserved", "value": "export"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "{"}, {"type": "Text", "value": " "}, {"type": "NameOther", "value": "TestComponent"}, {"type": "Text", "value": " "}, {"type": "Punctuation", "value": "}"}]