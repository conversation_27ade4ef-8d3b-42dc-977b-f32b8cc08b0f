on:
  push:
    branches:
      - master
  pull_request:
name: CI
jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Init Hermit
        run: ./bin/hermit env -r >> $GITHUB_ENV
      - name: Test
        run: go test ./...
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Init Hermit
        run: ./bin/hermit env -r >> $GITHUB_ENV
      - name: golangci-lint
        run: golangci-lint run
