name: test
on:
  push:
    branches:
      - master
  pull_request:
    branches:
      - master
jobs:
  test:
    strategy:
      matrix:
        os: [windows-latest, macos-latest, ubuntu-latest]
        go:
          - "1.15"
          - "1.16"
          - "1.17"
          - "1.18"
          - "1.19"
          - "1.20"
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v4
        with:
          go-version: ${{ matrix.go }}

      - run: go generate ./...
      - run: git diff --cached --exit-code
      - run: go test ./... -v -cover -coverprofile coverage.out
      - run: go test -bench . -benchmem

      - uses: codecov/codecov-action@v3
