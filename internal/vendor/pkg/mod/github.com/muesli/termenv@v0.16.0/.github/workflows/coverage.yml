name: coverage
on: [push, pull_request]

jobs:
  coverage:
    strategy:
      matrix:
        go-version: [^1]
        os: [ubuntu-latest]
    runs-on: ${{ matrix.os }}
    env:
      GO111MODULE: "on"
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ matrix.go-version }}

      - name: Coverage
        run: go test -race -covermode atomic -coverprofile=profile.cov ./...

      - name: Send coverage
        uses: shogo82148/actions-goveralls@v1
        with:
          path-to-profile: profile.cov
        env:
          COVERALLS_TOKEN: ${{ secrets.GITHUB_TOKEN }}
