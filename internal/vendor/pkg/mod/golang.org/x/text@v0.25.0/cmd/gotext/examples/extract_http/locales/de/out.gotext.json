{"language": "de", "messages": [{"id": "Hello {From}!", "key": "Hello %s!\n", "message": "Hello {From}!", "translation": "", "placeholders": [{"id": "From", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "<PERSON>.<PERSON><PERSON>.Get(\"From\")"}], "position": "golang.org/x/text/cmd/gotext/examples/extract_http/pkg/pkg.go:22:11"}, {"id": "Do you like your browser ({User_Agent})?", "key": "Do you like your browser (%s)?\n", "message": "Do you like your browser ({User_Agent})?", "translation": "", "placeholders": [{"id": "User_Agent", "string": "%[1]s", "type": "string", "underlyingType": "string", "argNum": 1, "expr": "<PERSON>.<PERSON><PERSON>.<PERSON>(\"User-Agent\")"}], "position": "golang.org/x/text/cmd/gotext/examples/extract_http/pkg/pkg.go:24:11"}]}