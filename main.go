package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/weaviate/weaviate-go-client/v5/weaviate"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/auth"
)

func main() {
	fmt.Println("🚀 Starting Weaviate Example Application")

	// Initialize Weaviate client
	client, err := initializeWeaviateClient()
	if err != nil {
		log.Fatalf("Failed to initialize Weaviate client: %v", err)
	}

	// Check if Weaviate is ready
	if err := checkWeaviateHealth(client); err != nil {
		log.Fatalf("Weaviate health check failed: %v", err)
	}

	fmt.Println("✅ Connected to Weaviate successfully")

	// Create collections
	fmt.Println("\n📚 Creating collections...")
	if err := createCollections(client); err != nil {
		log.Fatalf("Failed to create collections: %v", err)
	}

	// Insert sample data
	fmt.Println("\n📝 Inserting sample data...")
	if err := insertSampleData(client); err != nil {
		log.Fatalf("Failed to insert sample data: %v", err)
	}

	// Wait a moment for indexing
	fmt.Println("\n⏳ Waiting for indexing...")
	time.Sleep(2 * time.Second)

	// Demonstrate various search operations
	fmt.Println("\n🔍 Demonstrating search operations...")
	demonstrateSearches(client)

	fmt.Println("\n🎉 Example completed successfully!")
}

// initializeWeaviateClient creates and configures a Weaviate client
func initializeWeaviateClient() (*weaviate.Client, error) {
	cfg := weaviate.Config{
		Host:   "localhost:8080",
		Scheme: "http",
	}

	// For Weaviate Cloud Service (WCS), use:
	// cfg := weaviate.Config{
	//     Host:   "your-cluster-url.weaviate.network",
	//     Scheme: "https",
	//     AuthConfig: auth.ApiKey{Value: "your-api-key"},
	//     Headers: map[string]string{
	//         "X-OpenAI-Api-Key": "your-openai-api-key", // if using OpenAI
	//     },
	// }

	client, err := weaviate.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("failed to create client: %w", err)
	}

	return client, nil
}

// checkWeaviateHealth verifies that Weaviate is accessible and ready
func checkWeaviateHealth(client *weaviate.Client) error {
	ctx := context.Background()
	
	ready, err := client.Misc().ReadyChecker().Do(ctx)
	if err != nil {
		return fmt.Errorf("failed to check readiness: %w", err)
	}

	if !ready {
		return fmt.Errorf("weaviate is not ready")
	}

	return nil
}

// demonstrateSearches shows various search capabilities
func demonstrateSearches(client *weaviate.Client) {
	fmt.Println("\n🔍 Vector Search Example:")
	performVectorSearch(client, "technology innovation artificial intelligence")

	fmt.Println("\n🔍 Keyword Search Example:")
	performKeywordSearch(client, "golang programming")

	fmt.Println("\n🔍 Hybrid Search Example:")
	performHybridSearch(client, "machine learning tutorial")

	fmt.Println("\n🔍 Filtered Search Example:")
	performFilteredSearch(client, "Technology")
}
