package main

import (
	"testing"
	"time"

	"github.com/google/uuid"
)

func TestArticleModel(t *testing.T) {
	article := Article{
		ID:          uuid.New().String(),
		Title:       "Test Article",
		Content:     "This is a test article content",
		Author:      "Test Author",
		Category:    "Test",
		PublishedAt: time.Now(),
		Tags:        []string{"test", "example"},
		URL:         "https://example.com/test",
	}

	if article.Title != "Test Article" {
		t.Errorf("Expected title 'Test Article', got '%s'", article.Title)
	}

	if len(article.Tags) != 2 {
		t.<PERSON><PERSON><PERSON>("Expected 2 tags, got %d", len(article.Tags))
	}

	if article.Tags[0] != "test" {
		t.<PERSON><PERSON>("Expected first tag 'test', got '%s'", article.Tags[0])
	}
}

func TestProductModel(t *testing.T) {
	product := Product{
		ID:          uuid.New().String(),
		Name:        "Test Product",
		Description: "This is a test product",
		Category:    "Test Category",
		Brand:       "Test Brand",
		Price:       99.99,
		InStock:     true,
		Tags:        []string{"test", "product"},
	}

	if product.Name != "Test Product" {
		t.<PERSON>("Expected name 'Test Product', got '%s'", product.Name)
	}

	if product.Price != 99.99 {
		t.Errorf("Expected price 99.99, got %f", product.Price)
	}

	if !product.InStock {
		t.Error("Expected product to be in stock")
	}

	if len(product.Tags) != 2 {
		t.Errorf("Expected 2 tags, got %d", len(product.Tags))
	}
}

func TestSearchResult(t *testing.T) {
	result := SearchResult{
		ID:       uuid.New().String(),
		Score:    0.95,
		Object:   map[string]interface{}{"title": "Test"},
		Distance: 0.05,
	}

	if result.Score != 0.95 {
		t.Errorf("Expected score 0.95, got %f", result.Score)
	}

	if result.Distance != 0.05 {
		t.Errorf("Expected distance 0.05, got %f", result.Distance)
	}

	if title, ok := result.Object["title"]; !ok || title != "Test" {
		t.Errorf("Expected object title 'Test', got %v", title)
	}
}
