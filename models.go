package main

import "time"

// Article represents a news article or blog post
type Article struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Content     string    `json:"content"`
	Author      string    `json:"author"`
	Category    string    `json:"category"`
	PublishedAt time.Time `json:"published_at"`
	Tags        []string  `json:"tags"`
	URL         string    `json:"url"`
}

// Product represents an e-commerce product
type Product struct {
	ID          string  `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Category    string  `json:"category"`
	Brand       string  `json:"brand"`
	Price       float64 `json:"price"`
	InStock     bool    `json:"in_stock"`
	Tags        []string `json:"tags"`
}

// SearchResult represents a generic search result
type SearchResult struct {
	ID       string                 `json:"id"`
	Score    float64               `json:"score"`
	Object   map[string]interface{} `json:"object"`
	Distance float64               `json:"distance"`
}
