package openaiservice

import (
	"context"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/oxio/aia/pkg/common"
)

type OpenAiService struct {
	cfg    common.HttpClientConfig
	client *openai.Client
}

func New(cfg common.HttpClientConfig) *OpenAiService {
	return &OpenAiService{cfg: cfg}
}

func (s *OpenAiService) getClient() *openai.Client {
	if s.client == nil {
		s.client = openai.NewClient(
			option.WithBaseURL(s.cfg.BaseUrl),
			option.WithAPIKey(s.cfg.APIKey),
		)
	}
	return s.client
}

func (s *OpenAiService) GetAvailableModels(ctx context.Context) ([]string, error) {
	models, err := s.getClient().Models.List(ctx)
	if err != nil {
		return nil, err
	}

	var modelNames []string
	for _, model := range models.Data {
		modelNames = append(modelNames, model.ID)
	}

	return modelNames, nil
}
